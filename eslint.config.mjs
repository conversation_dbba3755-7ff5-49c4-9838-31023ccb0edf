import js from "@eslint/js";
import globals from "globals";
import pluginReact from "eslint-plugin-react";
import { defineConfig } from "eslint/config";
import eslintPluginUnicorn from 'eslint-plugin-unicorn';
import pluginImport from 'eslint-plugin-import';

export default defineConfig([
  {
    files: ["**/*.{js,mjs,cjs,jsx}"],
    plugins: {
      js,
      import: pluginImport,
    },
    extends: ["js/recommended"]
  },
  pluginReact.configs.flat.recommended,
  eslintPluginUnicorn.configs.recommended,
  {
    files: ["**/*.{js,mjs,cjs,jsx}"],
    settings: {
      react: {
        version: "detect", // Automatically detect React version
      },
      "import/resolver": {
        webpack: {
          config: "webpack.config.js",
        },
      },
    },
    rules: {
      'unicorn/better-regex': 'warn',
      'unicorn/prevent-abbreviations': 'off',
      "unicorn/no-null": 'off',
      "unicorn/no-negated-condition": 'off',
      "quotes": "error",

      // React-specific rules
      'react/prop-types': 'off',
      'react/jsx-uses-react': 'error',
      'react/jsx-uses-vars': 'error',
      // Common JavaScript rules
      'no-unused-vars': 'warn',
      'no-console': 'warn',
      // Node.js globals
      'no-process-env': 'off',

      "linebreak-style": 'off',
      "template-curly-spacing": 'warn',
      "indent": [
        "error",
        2,
        {
          "ignoredNodes": [
            "TemplateLiteral"
          ]
        }
      ],
      "no-invalid-this": 'warn',
      "class-methods-use-this": 'warn',
      "no-bitwise": 'off',
      "no-plusplus": 'off',
      "react/jsx-fragments": [1, "element"],
      "import/no-cycle": 'off',
      "no-multiple-empty-lines": ["error", { "max": 1, "maxBOF": 0, "maxEOF": 0 }]
    },
    // Add Node.js globals for build scripts
    languageOptions: {
      globals: {
        ...globals.browser,
        ...globals.node,
      }
    }
  },

]);
