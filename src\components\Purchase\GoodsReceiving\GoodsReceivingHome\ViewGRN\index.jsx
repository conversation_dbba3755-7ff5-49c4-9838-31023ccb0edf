// React Components
import React, { Component, Fragment } from 'react';
import { with<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { connect } from 'react-redux';

// Ant Design Components
import {
  Table, Drawer, Popconfirm, Menu, Dropdown, Tabs, Badge, Rate, Tooltip, Select, DatePicker, notification,
} from 'antd';
import {
  EditOutlined, LoadingOutlined, CaretRightOutlined, CaretDownOutlined, UserOutlined, LinkOutlined, InfoCircleOutlined, EditFilled, CheckOutlined, MailOutlined, WhatsAppOutlined,
} from '@ant-design/icons';

// FontAwesome
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faBarcode, faPrint } from '@fortawesome/free-solid-svg-icons';

// Utilities & Helpers
import { v4 as uuidv4 } from 'uuid';
import dayjs from 'dayjs';
import Constants, {
  DEFAULT_CUR_ROUND_OFF, QUANTITY, toISTDate, entityNameEnum,
} from '@Apis/constants';
import Helpers from '@Apis/helpers';
import CustomFieldHelpers from '@Helpers/CustomFieldHelpers';
import { getLinkedDocumentInfo, getQcData, handleFileChange } from './helpers';
import ViewGRNHeader from './ViewGRNHeader';

// UI Components
import H3Text from '@Uilib/h3Text';
import H3Image from '@Uilib/h3Image';
import H3Progress from '@Components/Common/H3Progress';
import PRZModal from '../../../../Common/UI/PRZModal';
import PRZSelect from '../../../../Common/UI/PRZSelect';

// Images
import closeIcon from '@Images/icons/icon-close-blue.png';
import imageDefault from '@Images/icons/imageDefault.png';
import Busy from '@Images/admin/integrations/busy.png';
import TallyPrime from '@Images/admin/integrations/tally-prime.png';
import zohoIcon from '@Images/admin/integrations/zoho-books.png';
import Crown from '@Images/crown2.png';

// Actions
import GRNActions from '@Actions/grnActions';
import PaymentOutgoingActions from '@Actions/paymentOutgoingActions';
import AttachmentActions from '@Actions/attachmentActions';
import ActivityLogActions from '@Actions/activityLogActions';
import ZohoIntegrationActions from '@Actions/integrations/zohoIntegrationActions';
import TallyIntegrationActions from '@Actions/integrations/tallyIntegrationActions';
import QualityCheckActions from '@Actions/quality/qualityCheckActions';
import AnalyticsActions from '@Actions/application/analyticsActions';
import BusyIntegrationActions from '../../../../../actions/integrations/busyIntegrationAction';

// Common Components
import ActivityLog from '@Components/Common/ActivityLog';
import ViewPayment from '@Components/Common/ViewPayment';
import Attachment from '@Components/Common/Attachment';
import ViewCustomFields from '@Components/Common/CustomField/ViewCustomFields';
import WorkFlowTimeLine from '@Components/Common/WorkFlowTimeLine';
import TagSelector from '@Components/Common/Selector/TagSelector';
import ProductCategoryLabel from '../../../../Common/ProductCategoryLabel';
import HideComponent from '../../../../Common/RestrictedAccess/HideComponent';
import HideValue from '../../../../Common/RestrictedAccess/HideValue';
import VendorRating from '../../../../Common/VendorRating';
import DownloadLabels from '../../../../Common/DownloadLabels';
import ViewLoadingSkull from '../../../../Common/ViewLoadingSkull';
import ViewDocRightSection from '../../../../Common/ViewDocRightSection';
import ViewGRNFooter from './ViewGRNFooter';
import ViewGRNLines from './ViewGRNLines';

// GRN & Related
import QualityCheckGRN from './QualityCheckGRN';
import GrnMail from './GrnMail';

// Payment Components
import RecordOutgoingPayment from '../../../../Payments/RecordPayment/RecordOutgoingPayment';
import LinkOutgoingPayment from '../../../../Payments/LinkPayment/LinkOutgoingPayment';

// Purchase Components
import DebitNoteForm from '@Components/Purchase/DebitNotes/DebitNoteForm';
import ViewDebitNote from '@Components/Purchase/DebitNotes/ViewDebitNote';
import AccountPayableInvoiceForm from '../../../AccountPayableInvoice/AccountPayableInvoiceForm';
import SendWhatsapp from '@Components/Common/ViewPurchaseOrder/SendWhatsapp';

// Inventory Components
import BatchesTable from '@Components/Common/BatchesTable';
import PrintBarcodes from '@Components/Inventory/PrintBarcodes';

// Styles
import './style.scss';

const { TabPane } = Tabs;

class ViewGRN extends Component {
  constructor(props) {
    super(props);
    this.fileInputRef = React.createRef();
    this.handleFileChangeEWayBill = this.handleFileChangeEWayBill.bind(this);
    this.state = {
      currentTab: '/goods-received',
      isGetAttachment: false,
      showModal: false,
      isUpdateRating: false,
      isGenerateLabelModalOpen: false,
      isLabelAvailable: false,
      eWayBillAttachment: [],
      adjustmentColumns: [
        {
          title: '',
          responsive: ['xs'],
          render: (item) => (
            <div className="mobile-list__item">
              <div onClick={() => this.setState({ showPRZModal: true, selectedDocumentId: item?.product_sku_info?.product_sku_id })}>
                <a>
                  {`#${item?.product_sku_info?.internal_sku_code}`}
                </a>
              </div>
              <H3Text text={`Product Name - ${item.remarks}`} className="mobile-list__item-info" />
              <H3Text text={`Received  Quantity: - ${QUANTITY(item?.received_qty, item?.uom_info?.precision)} ${item?.uom_info?.[0]?.uqc?.toProperCase()})`} className="mobile-list__item-info" />
            </div>
          ),
        },
        {
          title: '',
          responsive: ['sm', 'md', 'lg', 'xxl'],
          render: (item) => (
            <div style={{ width: '50px', height: '50px' }}>
              <H3Image alt="product" style={{ width: '50px', height: '50px', opacity: item?.product_sku_info?.assets?.[0]?.url ? 1 : 0.15 }} src={item?.product_sku_info?.assets?.[0]?.url ? item?.product_sku_info?.assets?.[0]?.url : imageDefault} />
            </div>
          ),
        },
        {
          title: 'SKU#',
          responsive: ['sm', 'md', 'lg', 'xxl'],
          render: (record) => (
            <Link to={`/inventory/product/view/${record?.product_sku_info?.product_sku_id}`} target="_blank">
              {record?.product_sku_info?.internal_sku_code}
            </Link>
          ),
        },
        {
          title: 'Product Name',
          responsive: ['sm', 'md', 'lg', 'xxl'],
          render: (item) => (
            <div style={{ Width: '100px' }}>
              {item?.product_sku_info?.product_sku_name}
              <H3Text text={item.remarks} className="table-subscript" />
            </div>
          ),
        },
        {
          title: 'Received  Quantity',
          responsive: ['sm', 'md', 'lg', 'xxl'],
          render: (record) => `${QUANTITY(record?.received_qty, record?.uom_info?.precision)} ${record?.uom_info?.[0]?.uqc?.toProperCase()}`,
        },
      ],
      currentUpdate: '',
      visibleColumns: {
        SKU: {
          label: 'SKU#',
          visible: true,
          disabled: true,
        },
        RECEIVED: {
          label: 'Received',
          visible: true,
          disabled: true,
        },
        INVOICE_QUANTITY: {
          label: 'Invoice Quantity',
          visible: true,
          disabled: true,
        },
        UNIT_PRICE: {
          label: 'Unit Price',
          visible: true,
          disabled: true,
        },
        DISCOUNT: {
          label: 'Discount',
          visible: true,
          disabled: true,
        },
        TOTAL: {
          label: 'Total',
          visible: true,
          disabled: true,
        },
      },
    };
  }

  getGRNColumns() {
    const { user, priceMasking, selectedGRN } = this.props;
    const { cfGRNLine, visibleColumns, isApInvoiceEnabled } = this.state;
    const { isDataMaskingPolicyEnable, isHideCostPrice, isHideSellingPrice } = priceMasking;

    const isVendorOverseas = (selectedGRN?.seller_info?.seller_type === 'OVERSEAS' || selectedGRN?.seller_type === 'OVERSEAS') && user?.tenant_info?.purchase_config?.sub_modules?.vendor?.settings?.hide_tax_for_overseas;

    const grnColumns = [
      {
        title: '',
        responsive: ['xs'],
        render: (item) => (
          <div className="mobile-list__item">
            <Link to={`/inventory/product/view/${item?.product_sku_info?.product_sku_id}`} target="_blank">
              <H3Text text={`SKU# : ${item?.product_sku_info?.internal_sku_code}`} className="mobile-list__item-number" />
            </Link>

            <H3Text text={`Product Name: ${item?.product_sku_info?.product_sku_name}`} className="mobile-list__item-info" />
            <H3Text text={`Ordered Qty: ${QUANTITY(item?.ordered_qty || '0', item?.uom_info[0]?.precision)} ${item?.uom_info?.[0]?.uqc?.toProperCase()})`} className="mobile-list__item-info" />
            <H3Text text={`Received Qty: ${QUANTITY(item?.received_qty, item?.uom_info?.precision)} ${item?.uom_info?.[0]?.uqc?.toProperCase()})`} className="mobile-list__item-info" />
            <H3Text text={`Returned Qty: ${QUANTITY(item?.received_qty || '0', item?.uom_info[0]?.precision)} ${item?.uom_info[0]?.uqc?.toProperCase()}`} className="mobile-list__item-info" />
            {!isApInvoiceEnabled && (<H3Text text={`Unit Price: ${this.props.MONEY(item?.offer_price)}`} className="mobile-list__item-info" />)}
            {(selectedGRN?.seller_info?.seller_type !== 'OVERSEAS' && !isApInvoiceEnabled) && (<H3Text text={`Tax: ${item?.tax_info?.tax_value}%`} className="mobile-list__item-info" />)}
            {!isApInvoiceEnabled && (<H3Text text={`Total: ${this.props.MONEY(Number(item.received_qty) * Number(item.offer_price) * (1 + Number(item?.tax_info?.tax_value) / 100))}`} className="mobile-list__item-info" />)}
          </div>
        ),
      },
      {
        title: 'Product',
        responsive: ['sm', 'md', 'lg', 'xxl'],
        render: (item) => (
          <div style={{ maxWidth: '220px' }}>
            <Link
              onClick={() => this.setState({ showPRZModal: true, selectedDocumentId: item?.product_sku_info?.product_sku_id })}
            >
              {`#${item?.product_sku_info?.internal_sku_code}`}
            </Link>
            <div>
              {`${item?.product_sku_info?.ref_product_code ? `${item?.product_sku_info?.ref_product_code} - ` : ''}${item?.product_sku_info?.product_sku_name}`?.trim()}
              <div
                dangerouslySetInnerHTML={{ __html: item?.remarks }}
                className="table-subscript"
                style={{
                  width: '200px',
                  whiteSpace: 'pre-wrap',
                }}
              />
            </div>
            {item?.po_number &&
              <div>
                <Link
                  to={`/approval?type=po&id=${item?.po_id}`}
                  target="_blank"
                >
                  {`${item?.po_number || ''}`}
                </Link>
              </div>
            }
            {item?.product_sku_info?.product_category_info?.category_path?.length > 0 && (
              <ProductCategoryLabel
                categoryPath={item?.product_sku_info?.product_category_info?.category_path}
                categoryName={item?.product_sku_info?.product_category_info?.category_path?.at(
                  -1
                )}
                containerStyle={{
                  width: "fit-content",
                }}
              />
            )}
          </div>
        ),
        visible: visibleColumns?.SKU?.visible,
      },
      {
        title: 'Received',
        responsive: ['sm', 'md', 'lg', 'xxl'],
        render: (record) => (
          <div>
            {this.props?.selectedGRN?.grn_entity_type !== 'GOOD_RECEIVING_NOTE' ? (
              <Tooltip title={(
                <div>
                  <H3Text
                    text={`Ordered - ${record?.ordered_qty || '0'} ${record?.uom_info?.[0]?.uqc?.toProperCase() || ''}`}
                  />
                  <H3Text
                    text={`Received - ${record?.received_qty} ${record?.uom_info?.[0]?.uqc?.toProperCase() || ''}`}
                  />
                </div>
              )}
              >
                {`${record?.received_qty} ${record?.uom_info?.[0]?.uqc?.toProperCase() || ''}`}
                <H3Progress percent={parseInt((record?.received_qty / (record?.ordered_qty > 0 ? record?.ordered_qty : 1)) * 100)} barWidth="80px" />
              </Tooltip>
            ) : <Fragment>{`${record?.received_qty} ${record?.uom_info?.[0]?.uqc?.toProperCase() || ''}`}</Fragment>}
            {record?.secondary_uom_qty > 0 && user?.tenant_info?.global_config?.settings?.enable_secondary_uom && (
              <div>
                {`${QUANTITY(record?.secondary_uom_qty, (record?.product_sku_info?.secondary_uom_info?.precision || 0))} ${record?.product_sku_info?.secondary_uom_info?.uqc?.toProperCase() || 'N/A'} (Secondary)`}
              </div>
            )}
            {Number(record?.dn_qty) > 0 && (
              <H3Text
                text={`(${QUANTITY(record?.dn_qty || '0', record?.uom_info[0]?.precision)} ${record?.uom_info[0]?.uqc?.toProperCase()} Returned)`}
                className="danger-subtext"
              />
            )}
            {(this.props?.selectedGRN?.grn_entity_type == 'PURCHASE_ORDER' && record?.ordered_qty <= 0) ? <div className="status-tag" style={{ backgroundColor: '#468bf7' }}>ADHOC</div> : ''}
          </div>
        ),
        visible: visibleColumns?.RECEIVED?.visible,
      },
      {
        title: 'Invoice Quantity',
        responsive: ['sm', 'md', 'lg', 'xxl'],
        render: (record) => `${QUANTITY(record?.invoice_quantity, record?.uom_info?.precision)} ${record?.uom_info?.[0]?.uqc?.toProperCase()}`,
        visible: isApInvoiceEnabled && visibleColumns?.INVOICE_QUANTITY?.visible,
      },
      {
        title: 'Unit Price',
        responsive: ['sm', 'md', 'lg', 'xxl'],
        render: (item) => {

          return (
            <div>
              {(isDataMaskingPolicyEnable && isHideCostPrice) ? <HideValue showPopOver popOverMessage={"You don't have access to view unit price"} /> : this.props.MONEY((item?.offer_price), this.props?.selectedGRN?.org_currency_info?.currency_code)}
              {!isVendorOverseas && (<H3Text
                text={`+ tax@${item?.tax_info?.tax_value}%`}
                className="danger-subtext"
              />)}
            </div>
          )
        },
        visible: !isApInvoiceEnabled && visibleColumns?.UNIT_PRICE?.visible,
      },
      {
        title: 'Discount',
        responsive: ['sm', 'md', 'lg', 'xxl'],
        render: (record) => (
          <Fragment>
            {
              Number(record?.line_discount_amount) > 0
                ? (
                  <Fragment>
                    {(record?.is_discount_in_percent ? `${Number(record?.line_discount_percentage).toPrecision(DEFAULT_CUR_ROUND_OFF) || '0'}%` : this.props.MONEY(record?.line_discount_amount, this.props?.selectedGRN?.org_currency_info?.currency_code))}
                  </Fragment>
                )
                : '-'
            }
          </Fragment>
        ),
        visible: !isApInvoiceEnabled && visibleColumns?.DISCOUNT?.visible,
        className: !this.props.selectedGRN?.is_line_wise_discount ? 'display-none' : '',
      },
      {
        title: 'Total',
        responsive: ['sm', 'md', 'lg', 'xxl'],
        render: (item) => (isDataMaskingPolicyEnable && isHideCostPrice) ? <HideValue showPopOver popOverMessage={"You don't have access to view unit price"} /> : this.props.MONEY((((Number(item.received_qty) * Number(item.offer_price)) * (item.line_discount_percentage ? Number(100 - item.line_discount_percentage) / 100 : 1)) * (1 + Number(item?.tax_info?.tax_value) / 100)), this.props?.selectedGRN?.org_currency_info?.currency_code),
        visible: !isApInvoiceEnabled && visibleColumns?.TOTAL?.visible,
      },
    ];

    if (cfGRNLine?.length) {
      grnColumns.splice(5, 0, ...CustomFieldHelpers.renderCustomLineColumns(false, cfGRNLine, visibleColumns));
    }

    return grnColumns?.filter((item) => item.visible) || [];
  }

  componentDidMount() {
    const {
      getGRNById, user, selectedGrnId, match,
    } = this.props;
    getGRNById(Helpers.getTenantEntityPermission(user?.user_tenants, Helpers.permissionEntities.GOOD_RECEIVING, Helpers.permissionTypes.READ).join(','), selectedGrnId || match?.params?.grnId);
  }

  componentWillUnmount() {
    const {
      getGRNByIdSuccess,
    } = this.props;
    getGRNByIdSuccess(null);
  }

  static getDerivedStateFromProps(props, state) {
    if (props?.selectedGRN && !state.isUserReadyForPrefillData) {
      const selectedGRN = props?.selectedGRN;
      const lineCf = selectedGRN?.grn_lines?.[0]?.grn_line_custom_fields;

      return {
        ...state,
        fileList: selectedGRN?.attachments,
        selectedTags: selectedGRN?.tags,
        isUserReadyForPrefillData: true,
        updatedGstNumber: selectedGRN?.tenant_seller_info?.gst_number,
        updateInvoiceNumber: selectedGRN?.invoice_number,
        updateEWayBillNumber: selectedGRN?.e_way_bill_number,
        updateInvoiceDate: selectedGRN?.invoice_date ? (toISTDate(selectedGRN?.invoice_date)) : null,
        updateDueDate: selectedGRN?.invoice_date ? (toISTDate(selectedGRN?.grn_due_date)) : null,
        cfGRNLine: lineCf,
        visibleColumns: CustomFieldHelpers.updateVisibleColumns(lineCf, state.visibleColumns),
        isApInvoiceEnabled: selectedGRN?.allow_grn_to_create_ap_invoice,
      };
    }
    if (props?.selectedAttachment && state.isGetAttachment && !state.getAttachmentByIdLoading && !state.updateAttachmentLoading) {
      return {
        ...state,
        fileList: props?.selectedAttachment,
        isGetAttachment: false,
      };
    }

    return state;
  }

  // handleFileChange(fileListData) {
  //   const { updateAttachment, selectedGRN, getAttachmentById } = this.props;
  //   const fileList = fileListData?.fileList?.map((item) => ({
  //     ...item,
  //     url: item?.response?.response?.location || item?.url,
  //   }));
  //   const attachments = fileList?.map((attachment) => ({
  //     url: attachment?.response?.response?.location || attachment?.url, type: attachment.type, name: attachment.name, uid: attachment.uid,
  //   }));
  //   if (fileListData?.file.status === 'done') {
  //     const payload = {
  //       entity_id: selectedGRN?.grn_id,
  //       entity_name: 'good_receiving_note',
  //       attachments,
  //     };
  //     updateAttachment(payload, () => {
  //       getAttachmentById(selectedGRN?.grn_id, 'good_receiving_note', () => {
  //         this.setState({
  //           isGetAttachment: true,

  //         });
  //       });
  //     });
  //   }
  //   if (fileListData?.file.status === 'removed') {
  //     const payload = {
  //       entity_id: selectedGRN?.grn_id,
  //       entity_name: 'good_receiving_note',
  //       attachments,
  //     };
  //     updateAttachment(payload, () => {
  //       getAttachmentById(selectedGRN?.grn_id, 'good_receiving_note', () => {
  //         this.setState({
  //           isGetAttachment: true,

  //         });
  //       });
  //     });
  //   }

  //   this.setState({
  //     fileList: attachments.length > 0 ? attachments : [],
  //   });
  // }

  // getQcData = (data) => {
  //   const { selectedGRN } = this.props;
  //   const requiredData = [];
  //   const productBatches = data?.map((item) => item?.product_batches)?.flat(1);
  //   const qualityChecks = productBatches?.map((productBatch) => productBatch?.quality_checks)?.flat(1);
  //   const qualityChecksArr = [];
  //   qualityChecks?.map((qualityCheck) => {
  //     data?.map((grnLine) => {
  //       if (qualityCheck?.entity_line_id === grnLine?.grn_line_id) {
  //         qualityChecksArr.push({
  //           ...qualityCheck,
  //           ordered_qty: grnLine?.ordered_qty,
  //           product_sku_info: grnLine?.product_sku_info,
  //           tenant_product_id: grnLine?.tenant_product_id,
  //           expiry_date: grnLine?.expiry_date,
  //           available_batches: grnLine?.available_batches,
  //           tax_info: grnLine?.tax_info,
  //           uom_info: grnLine?.uom_info,
  //           uom_list: grnLine?.product_sku_info?.uom_list,
  //           tenant_department_id: selectedGRN?.tenant_department_id,
  //         });
  //       }
  //     });
  //   });
  //   qualityChecksArr?.map((qualityCheck) => {
  //     productBatches?.map((productBatch) => {
  //       if (productBatch?.batch_id === qualityCheck?.batch_id) {
  //         requiredData.push({
  //           ...qualityCheck,
  //           custom_batch_number: productBatch?.custom_batch_number,
  //           lot_number: productBatch?.lot_number,
  //           batch_number: productBatch?.batch_number,
  //           mrp: productBatch?.mrp,
  //           cost_price: productBatch?.cost_price,
  //           inventory_location_id: productBatch?.inventory_location_id,
  //           inventory_location_name: productBatch?.inventory_location_name,
  //           inventory_location_path: productBatch?.inventory_location_path,
  //           is_rejected_batch: productBatch?.is_rejected_batch,
  //           selling_price: productBatch?.selling_price,
  //           expiry_date: productBatch?.expiry_date,
  //           quantity: productBatch?.quantity,
  //         });
  //       }
  //     });
  //   });
  //   return requiredData;
  // };

  // renderFreight(position) {
  //   const { selectedGRN, MONEY, user } = this.props;
  //   const isVendorOverseas = selectedGRN?.seller_info?.seller_type === 'OVERSEAS' && user?.tenant_info?.purchase_config?.sub_modules?.vendor?.settings?.hide_tax_for_overseas;
  //   if ((selectedGRN?.charge_1_value > 0)) {
  //     if (position === 'top' && selectedGRN?.freight_tax_id) {
  //       return (
  //         <div className="view-document__totals-field">
  //           <H3Text
  //             text={(
  //               <Fragment>
  //                 {selectedGRN?.charge_1_name}
  //                 &nbsp;
  //                 {!isVendorOverseas && (<span className="table-subscript">{`tax@${selectedGRN?.freight_tax_info?.tax_value}%`}</span>)}
  //               </Fragment>
  //             )}
  //             className="view-document__totals-field-name"
  //           />
  //           <H3Text text={MONEY((selectedGRN?.charge_1_value), selectedGRN?.org_currency_info?.currency_code)} className="view-document__totals-field-value" />
  //         </div>
  //       );
  //     }
  //     if (position === 'bottom' && !selectedGRN?.freight_tax_id) {
  //       return (
  //         <div className="view-document__totals-field">
  //           <H3Text text={selectedGRN?.charge_1_name} className="view-document__totals-field-name" />
  //           <H3Text text={MONEY((selectedGRN?.charge_1_value), selectedGRN?.org_currency_info?.currency_code)} className="view-document__totals-field-value" />
  //         </div>
  //       );
  //     }
  //   }
  //   return '';
  // }

  handleFileChangeEWayBill(event) {
    const file = event.target.files[0];
    if (file) {
      const formData = new FormData();
      formData.append('file', file);

      fetch(Constants.UPLOAD_FILE, {
        method: 'POST',
        body: formData,
      })
        .then((response) => response.json())
        .then((data) => {
          this.setState({ eWayBillAttachment: data });
        })
        .catch((error) => {
          console.error('Error uploading file:', error);
        });
    }
  }

  render() {
    const {
      selectedGRN, createGRNLoading, updateGRNStatus, callback, updateGRNStatusLoading, deleteGrnLoading, deleteGrn, pushGrnToBusy, pushGrnToBusyLoading,
      user, getGRNByIdLoading, getGRNById, selectedGrnId, getPaymentsOutgoingSuccess, syncGRN, pullTallyVendorPayment, pullTallyVendorPaymentLoading, syncTallyGRN, syncTallyGRNLoading,
      match, isQuickView, history, destinationTenantId, getAttachmentByIdLoading, updateAttachmentLoading, syncGRNLoading, updateGRNWorkflowStep, updateGRNWorkflowStepLoading, MONEY,
      quickUpdateGRN, quickUpdateGRNLoading, getDownloadQualityChecks, downloadDocument, getActivityLog, showGrnAndLabelDropDown, priceMasking, updateAttachment, getAttachmentById,
    } = this.props;
    const {
      showPayment, adjustmentColumns, linkPayment, currentUpdate, showDnForm, updateInvoiceDate, updateDueDate, showEmailDrawer, showWhatsappDrawer, isGstNumberEditable, updatedGstNumber,
      openDNModalView, selectedDebitNote, showViewPayout, selectedPayment, fileList, currentTab, updateEWayBillNumber, isEWayBillNumber, eWayBillAttachment, cfGRNLine, isGenerateLabelModalOpen,
      selectedRowKeys, selectedRows, showPrintBarcodeDrawer, showModal, isUpdateRating, selectedTags, isInvoiceNumber, updateInvoiceNumber, isEWayBillAttachment, isLabelAvailable, showPRZModal, selectedDocumentId, isApInvoiceEnabled, showApInvoiceForm, visibleColumns,
    } = this.state;

    const isTallyConnected = (selectedGRN?.tenant_info?.tally_configuration?.purchase_voucher_integration) && (['ISSUED', 'VOID'].includes(selectedGRN?.status)) && (selectedGRN?.grn_entity_type !== 'INVENTORY_TRANSFER');
    const quickUpdateDisable = !(['VOID', 'REJECTED']?.includes(selectedGRN?.status)) && Helpers.getPermission(Helpers.permissionEntities.GOOD_RECEIVING, Helpers.permissionTypes.UPDATE, user);
    const accountingGSTTransactionAsPerMaster = user?.tenant_info?.purchase_config?.sub_modules?.vendor?.settings?.gst_details_in_transaction === 'AS_PER_MASTER';

    const isVendorOverseas = (selectedGRN?.seller_info?.seller_type === 'OVERSEAS' || selectedGRN?.seller_type === 'OVERSEAS') && user?.tenant_info?.purchase_config?.sub_modules?.vendor?.settings?.hide_tax_for_overseas;

    function getRatingColor(rating) {
      if (rating >= 3) {
        return 'green'; // 3, 4, and 5 are green
      } if (rating >= 2) {
        return 'yellow'; // 2 and 3 are yellow
      } if (rating > 0) return 'red';
      return 'grey'; // 0 and 1 are red
    }

    // const HideAdhocApprovals = () => {
    //   if (selectedGRN?.workflow_steps) {
    //     const steps = Object?.values(selectedGRN?.workflow_steps)?.filter((step) => step !== null);
    //     const pendingSteps = steps?.filter((step) => step?.status === 'PENDING');
    //     return steps?.length < 10 && pendingSteps?.length === steps?.length;
    //   }
    //   return false;
    // };

    const enableLabelGeneration = user?.tenant_info?.global_config?.sub_modules?.label_generation?.is_active;
    const menu = (
      <Menu>
        {selectedGRN?.org_currency_info?.org_currency_id && (
          <Menu.Item>
            <H3Text
              text={`Download GRN (${selectedGRN?.org_currency_info?.currency_code})`}
              onClick={() => {
                downloadDocument({
                  url: `${Constants.GRN}/download?grn_id=${selectedGRN?.grn_id}&tenant_id=${selectedGRN?.tenant_info?.tenant_id}&download_document_in_base_currency=false`,
                  document_type: 'GOODS_RECEIVING_NOTE',
                  document_number: selectedGRN?.grn_number,
                  key: uuidv4(),
                });
              }}
              className="hide__in-mobile"
            />
          </Menu.Item>
        )}
        {(selectedGRN?.base_currency_info?.org_currency_id && selectedGRN?.org_currency_info?.org_currency_id !== selectedGRN?.base_currency_info?.org_currency_id) && (
          <Menu.Item>
            <H3Text
              text={`Download GRN (${selectedGRN?.base_currency_info?.currency_code})`}
              onClick={() => {
                downloadDocument({
                  url: `${Constants.GRN}/download?grn_id=${selectedGRN?.grn_id}&tenant_id=${selectedGRN?.tenant_info?.tenant_id}&download_document_in_base_currency=true`,
                  document_type: 'GOODS_RECEIVING_NOTE',
                  document_number: selectedGRN?.grn_number,
                  key: uuidv4(),
                });
              }}
              className="hide__in-mobile"
            />
          </Menu.Item>
        )}
        <Menu.Item>
          <div style={{ display: `${!enableLabelGeneration ? 'flex' : 'block'}`, alignItems: 'center' }}>
            <H3Text
              text="Generate Labels"
              onClick={() => {
                if (enableLabelGeneration) {
                  if (!isLabelAvailable) {
                    notification.open({
                      type: 'error',
                      message: 'No Label format is available for Goods Receiving Note to download',
                      duration: 4,
                      placement: 'top',
                    });
                  } else {
                    this.setState({ isGenerateLabelModalOpen: true });
                  }
                }
              }}
              className="hide__in-mobile"
            />
            {!enableLabelGeneration
              && (
                <Popconfirm
                  placement="topRight"
                  title="This feature is not accessible within your current plan to use this feature contact us."
                  onConfirm={() => window.Intercom('showNewMessage')}
                  okText="Contact Us"
                  cancelText="Cancel"
                >
                  <img
                    className="barcode-restrict"
                    src={Crown}
                    alt="premium"
                    style={{
                      marginLeft: '8px',
                    }}
                  />
                </Popconfirm>
              )}
          </div>
        </Menu.Item>
      </Menu>
    );

    const { isDataMaskingPolicyEnable, isHideCostPrice, isHideSellingPrice } = priceMasking;

    // const splitChargesData = (charge) => {
    //   const chargeWithTaxName = charge?.filter((line) => line?.tax_info?.tax_id) || [];
    //   const chargeWithoutTaxName = charge?.filter((line) => !line?.tax_info?.tax_id) || [];
    //   return { chargeWithTaxName, chargeWithoutTaxName };
    // };

    // const renderCharges = (charge) => !!charge?.length && charge?.map((otherCharge, i) => (
    //   <div key={i} className="view-document__totals-field">
    //     <H3Text
    //       text={(
    //         <Fragment>
    //           {otherCharge?.charge_name?.toProperCase()}&nbsp;
    //           {otherCharge?.tax_info?.tax_value && !isVendorOverseas ? (
    //             <span className="table-subscript">{`tax@${otherCharge.tax_info.tax_value}%`}</span>
    //           ) : null}
    //         </Fragment>
    //       )}
    //       className="view-document__totals-field-name" />
    //     <H3Text text={MONEY((otherCharge?.charge_amount), selectedGRN?.org_currency_info?.currency_code)} className="view-document__totals-field-value" />
    //   </div>
    // ))

    const fixedMenuBar = user?.side_menu_bar_type === 'FIXED';

    return (
      <Fragment>
        {(getGRNByIdLoading === false && !pushGrnToBusyLoading) ? (
          <div className={`view-document__wrapper ${!isQuickView ? 'view-document__wrapper-page' : ''}`}>
            <Drawer
              onClose={() => this.setState({ showDnForm: false })}
              open={showDnForm}
              width="77.5%"
              destroyOnClose
            >
              <div className="custom-drawer__header-wrapper">
                <div className="custom-drawer__header" style={{ width: 'calc(77.5% - 40px)' }}>
                  <H3Text text="Create Debit Note" className="custom-drawer__title" />
                  <H3Image src={closeIcon} className="custom-drawer__close-icon" onClick={() => this.setState({ showDnForm: false })} />
                </div>
              </div>
              <DebitNoteForm
                selectedGrnForDN={{
                  ...selectedGRN,
                  tenant_seller_info: {
                    ...((selectedGRN && selectedGRN.tenant_seller_info) || {}),
                    gst_number: updatedGstNumber,
                  },
                }}
                callback={() => {
                  this.setState({ showDnForm: false });
                  getGRNById(Helpers.getTenantEntityPermission(user?.user_tenants, Helpers.permissionEntities.GOOD_RECEIVING, Helpers.permissionTypes.READ).join(','), selectedGrnId || match?.params?.grnId);
                }}
                isQuickView
              />
            </Drawer>
            <Drawer
              open={showEmailDrawer}
              width="420px"
              mask
              onClose={() => this.setState({ showEmailDrawer: false })}
              destroyOnClose
            >
              <div className="custom-drawer__header-wrapper">
                <div className="custom-drawer__header" style={{ width: '375px' }}>
                  <H3Text text="Send GRN Notification" className="custom-drawer__title" />
                  <H3Image src={closeIcon} className="custom-drawer__close-icon" onClick={() => this.setState({ showEmailDrawer: false })} />
                </div>
              </div>
              <GrnMail selectedEntity={selectedGRN} callback={() => this.setState({ showEmailDrawer: false })} />
            </Drawer>
            <Drawer
              open={showWhatsappDrawer}
              width="360px"
              mask
              onClose={() => this.setState({ showWhatsappDrawer: false })}
              destroyOnClose
            >
              <div className="custom-drawer__header-wrapper">
                <div className="custom-drawer__header" style={{ width: '310px' }}>
                  <H3Text text="Share on Whatsapp" className="custom-drawer__title" />
                  <H3Image src={closeIcon} className="custom-drawer__close-icon" onClick={() => this.setState({ showWhatsappDrawer: false })} />
                </div>
              </div>
              <SendWhatsapp selectedEntity={selectedGRN} entityName="grn" callback={() => this.setState({ showWhatsappDrawer: false })} />
            </Drawer>
            <Drawer
              open={showPayment}
              width="420px"
              onClose={() => this.setState({ showPayment: false })}
              destroyOnClose
            >
              <div className="custom-drawer__header-wrapper">
                <div className="custom-drawer__header" style={{ width: '375px' }}>
                  <H3Text text="Record New Payment" className="custom-drawer__title" />
                  <H3Image src={closeIcon} className="custom-drawer__close-icon" onClick={() => this.setState({ showPayment: false })} />
                </div>
              </div>

              {/* <ListUnusedPayment
                selectedGRN={selectedGRN}
                callback={() => {
                  this.setState({ showPayment: false });
                  getGRNById(Helpers.getTenantEntityPermission(user?.user_tenants, Helpers.permissionEntities.GOOD_RECEIVING, Helpers.permissionTypes.READ).join(','), selectedGrnId || match?.params?.grnId);
                }}
              /> */}
              <RecordOutgoingPayment
                selectedGRN={selectedGRN}
                callback={() => {
                  this.setState({ showPayment: false });
                  getGRNById(Helpers.getTenantEntityPermission(user?.user_tenants, Helpers.permissionEntities.GOOD_RECEIVING, Helpers.permissionTypes.READ).join(','), selectedGrnId || match?.params?.grnId);
                }}
              />
            </Drawer>
            <Drawer
              open={linkPayment}
              width="820px"
              onClose={() => this.setState({ linkPayment: false })}
              destroyOnClose
            >
              <div className="custom-drawer__header-wrapper">
                <div className="custom-drawer__header" style={{ width: '775px' }}>
                  <H3Text text="Link Payment" className="custom-drawer__title" />
                  <H3Image src={closeIcon} className="custom-drawer__close-icon" onClick={() => this.setState({ linkPayment: false })} />
                </div>
              </div>
              {/* <LinkPayment
                selectedGRN={selectedGRN}
                callback={() => {
                  this.setState({ linkPayment: false });
                  getGRNById(Helpers.getTenantEntityPermission(user?.user_tenants, Helpers.permissionEntities.GOOD_RECEIVING, Helpers.permissionTypes.READ).join(','), selectedGrnId || match?.params?.grnId);
                  getPaymentsOutgoingSuccess(null);
                }}
                screen={"grn"}
              /> */}
              <LinkOutgoingPayment
                selectedEntity={selectedGRN}
                callback={() => {
                  this.setState({ linkPayment: false });
                  getGRNById(Helpers.getTenantEntityPermission(user?.user_tenants, Helpers.permissionEntities.GOOD_RECEIVING, Helpers.permissionTypes.READ).join(','), selectedGrnId || match?.params?.grnId);
                  getPaymentsOutgoingSuccess(null);
                }}
                screen={"grn"}
              />
            </Drawer>
            <Drawer
              onClose={() => this.setState({ showApInvoiceForm: false })}
              open={showApInvoiceForm}
              // visible
              width="77.5%"
              destroyOnClose
            >
              <div className="custom-drawer__header-wrapper">
                <div className="custom-drawer__header" style={{ width: 'calc(77.5% - 40px)' }}>
                  <H3Text text="Create AP Invoice" className="custom-drawer__title" />
                  <H3Image src={closeIcon} className="custom-drawer__close-icon" onClick={() => this.setState({ showApInvoiceForm: false })} />
                </div>
              </div>
              <AccountPayableInvoiceForm
                selectedGRNForAPInvoice={{
                  ...selectedGRN,
                  tenant_seller_info: {
                    ...((selectedGRN && selectedGRN.tenant_seller_info) || {}),
                    gst_number: updatedGstNumber,
                  },
                }}
                callback={() => {
                  this.setState({ showApInvoiceForm: false });
                  getGRNById(Helpers.getTenantEntityPermission(user?.user_tenants, Helpers.permissionEntities.GOOD_RECEIVING, Helpers.permissionTypes.READ).join(','), selectedGrnId || match?.params?.grnId);
                }}
                isQuickView
              />
            </Drawer>
            <div className="ant-row">
              <div className={`${fixedMenuBar ? 'ant-col-md-16' : 'ant-col-md-17'} ant-col-xs-24`}>
                <div className={`view-left__wrapper ${!isQuickView ? 'is-page-view' : ''}`}>
                  <ViewGRNHeader
                    selectedGRN={selectedGRN}
                    destinationTenantId={destinationTenantId}
                    user={user}
                    downloadDocument={downloadDocument}
                    isLabelAvailable={isLabelAvailable}
                    setState={(stateValue) => this.setState(stateValue)}
                    isQuickView={isQuickView}
                    updateGRNStatus={updateGRNStatus}
                    getGRNById={getGRNById}
                    selectedGrnId={selectedGrnId}
                    callback={callback}
                    match={match}
                    currentUpdate={currentUpdate}
                    updateGRNStatusLoading={updateGRNStatusLoading}
                    deleteGrn={deleteGrn}
                    deleteGrnLoading={deleteGrnLoading}
                    showGrnAndLabelDropDown={showGrnAndLabelDropDown}
                    selectedRows={selectedRows}
                    isApInvoiceEnabled={isApInvoiceEnabled}
                    syncGRN={syncGRN}
                    syncGRNLoading={syncGRNLoading}
                    getDownloadQualityChecks={getDownloadQualityChecks}
                    syncTallyGRN={syncTallyGRN}
                    syncTallyGRNLoading={syncTallyGRNLoading}
                    pullTallyVendorPayment={pullTallyVendorPayment}
                    pushGrnToBusy={pushGrnToBusy}
                  />
                  {/* <div className="view-document__title">
                    <div className="view-document__title-number-wrapper">
                      <H3Text text={selectedGRN?.grn_number} className="view-document__title-number" />
                      <div className="view-document__title-status" style={{ backgroundColor: Helpers.getStatusColor(selectedGRN?.status).color }}>
                        {selectedGRN?.status?.replaceAll('_', ' ')}
                      </div>
                    </div>
                    {destinationTenantId && destinationTenantId === user?.tenant_info?.tenant_id ? (
                      <div className="view-document__title-actions">
                        <div className="action-buttons">
                          {['DRAFT'].includes(selectedGRN?.status) && Helpers.getPermission(Helpers.permissionEntities?.GOOD_RECEIVING, Helpers.permissionTypes.UPDATE, user) && !selectedGRN?.subcontractor_mo_id && (
                            <H3Text
                              text={(
                                <Link to={`/purchase/goods-receiving/update/${selectedGRN?.grn_id}`}>
                                  <EditOutlined />
                                </Link>
                              )}
                              className="action-button"
                            />
                          )}

                          <Dropdown overlay={menu} trigger={['click']}>
                            <div
                              style={{
                                cursor: 'pointer',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                width: '40px',
                              }}
                              className="action-button"
                            >
                              <FontAwesomeIcon icon={faPrint} style={{ fontSize: '16px' }} />
                            </div>
                          </Dropdown>

                        </div>
                        {!isQuickView && (
                          <div className="action-buttons">
                            {Helpers.getPermission(Helpers.permissionEntities?.GOOD_RECEIVING,
                              Helpers.permissionTypes.CREATE, user) && selectedGRN?.status.toUpperCase() === 'DRAFT'
                              && (
                                <Popconfirm
                                  placement="topRight"
                                  title="Are you sure you want to issue this GRN?"
                                  onConfirm={() => {
                                    this.setState({ currentUpdate: 'SENT_FOR_APPROVAL' });
                                    updateGRNStatus({
                                      grn_id: selectedGRN?.grn_id,
                                      status: 'SENT_FOR_APPROVAL',
                                    }, () => {
                                      this.setState({ currentUpdate: '' });
                                      this.setState({ showModal: true });
                                      getGRNById(Helpers.getTenantEntityPermission(user?.user_tenants, Helpers.permissionEntities.GOOD_RECEIVING, Helpers.permissionTypes.READ).join(','), selectedGrnId || match?.params?.grnId);
                                      if (callback) callback();
                                    });
                                  }}
                                  okText="Yes"
                                  cancelText="No"
                                >
                                  <H3Text
                                    text={(
                                      <div>
                                        {(currentUpdate === 'SENT_FOR_APPROVAL' && updateGRNStatusLoading)
                                          ? <LoadingOutlined /> : (
                                            <span>Issue GRN</span>
                                          )}
                                      </div>
                                    )}
                                    className="action-button action-button-big"
                                  />
                                </Popconfirm>
                              )}

                            {Helpers.getPermission(Helpers.permissionEntities.GOOD_RECEIVING,
                              Helpers.permissionTypes.DELETE, user) && !selectedGRN?.grn_payments?.length
                              && selectedGRN?.status.toUpperCase() !== 'VOID' && selectedGRN?.status.toUpperCase() !== 'REJECTED'
                              && (
                                <Popconfirm
                                  placement="topRight"
                                  title="Are you sure you want to cancel this GRN?"
                                  onConfirm={() => {
                                    this.setState({ currentUpdate: 'VOID' });
                                    updateGRNStatus({
                                      grn_id: selectedGRN?.grn_id,
                                      grn_tenant_id: selectedGRN?.grn_tenant_id,
                                      grn_entity_type: 'PURCHASE_ORDER',
                                      status: 'VOID',
                                    }, () => {
                                      this.setState({ currentUpdate: '' });
                                      getGRNById(Helpers.getTenantEntityPermission(user?.user_tenants, Helpers.permissionEntities.GOOD_RECEIVING, Helpers.permissionTypes.READ).join(','), selectedGrnId || match?.params?.grnId);
                                      if (callback) callback();
                                    });
                                  }}
                                  okText="Yes"
                                  cancelText="No"
                                >
                                  <H3Text
                                    text={(
                                      <div>
                                        {(currentUpdate === 'VOID' && updateGRNStatusLoading)
                                          ? <LoadingOutlined /> : (
                                            <span>Cancel</span>
                                          )}
                                      </div>
                                    )}
                                    className="action-button action-button-big"
                                  />
                                </Popconfirm>
                              )}
                            {Helpers.getPermission(Helpers.permissionEntities.GOOD_RECEIVING,
                              Helpers.permissionTypes.DELETE, user)
                              && !selectedGRN?.grn_payments?.length
                              && (
                                <Popconfirm
                                  placement="topRight"
                                  title="Are you sure you want to delete this GRN permanently?"
                                  onConfirm={() => {
                                    this.setState({ currentUpdate: 'DELETE' });
                                    deleteGrn(selectedGRN?.grn_tenant_id, selectedGRN?.grn_id, () => {
                                      this.setState({ currentUpdate: '' });
                                      if (callback) { callback(); } else {
                                        history.push('/purchase/goods-receiving/');
                                      }
                                    });
                                  }}
                                  okText="Yes"
                                  cancelText="No"
                                >
                                  <H3Text
                                    text={(
                                      <div>
                                        {(currentUpdate === 'DELETE' && deleteGrnLoading) ? <LoadingOutlined /> : (
                                          <span>Delete</span>
                                        )}
                                      </div>
                                    )}
                                    className="action-button action-button-big"
                                  />
                                </Popconfirm>
                              )}
                          </div>
                        )}
                      </div>
                    ) : (
                      <div className="view-document__title-actions">
                        <div className="action-buttons">
                          {['DRAFT'].includes(selectedGRN?.status) && Helpers.getPermission(Helpers.permissionEntities?.GOOD_RECEIVING, Helpers.permissionTypes.UPDATE, user) && !selectedGRN?.subcontractor_mo_id && (
                            <H3Text
                              text={(
                                <Link to={`/purchase/goods-receiving/update/${selectedGRN?.grn_id}`}>
                                  <EditOutlined />
                                </Link>
                              )}
                              className="action-button"
                            />
                          )}
                          {Helpers.getPermission(Helpers.permissionEntities.GOOD_RECEIVING, Helpers.permissionTypes.UPDATE, user) && selectedGRN?.status === 'ISSUED' && (
                            <H3Text
                              text={(
                                <div onClick={() => this.setState({ showEmailDrawer: true })}>
                                  <MailOutlined />
                                </div>
                              )}
                              className="action-button"
                            />
                          )}
                          {user?.tenant_info?.integration_config?.sub_modules?.whatsapp?.is_active && user?.tenant_info?.integration_tenant_whatsapp_id && Helpers.getPermission(Helpers.permissionEntities.GOOD_RECEIVING, Helpers.permissionTypes.UPDATE, user) && selectedGRN?.status === 'ISSUED' && (
                            <H3Text
                              text={(
                                <div onClick={() => this.setState({ showWhatsappDrawer: true })}>
                                  <WhatsAppOutlined />
                                </div>
                              )}
                              className="action-button"
                            />
                          )}

                          <Dropdown overlay={menu} trigger={['click']} getPopupContainer={(triggerNode) => (showGrnAndLabelDropDown ? triggerNode.parentNode : document.body)} placement={showGrnAndLabelDropDown ? 'topLeft' : 'bottomLeft'}>
                            <div style={{
                              cursor: 'pointer',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              width: '40px',
                            }}
                            >
                              <FontAwesomeIcon icon={faPrint} style={{ fontSize: '16px' }} />
                            </div>
                          </Dropdown>
                          {selectedRows?.length > 0 && user?.tenant_info?.global_config?.sub_modules?.barcoding?.is_active && (
                            <H3Text
                              text={(
                                <Fragment>
                                  Print Barcodes
                                  &nbsp;
                                  <FontAwesomeIcon icon={faBarcode} />
                                </Fragment>
                              )}
                              className="action-button action-button-big"
                              onClick={() => this.setState({ showPrintBarcodeDrawer: true })}
                            />
                          )}
                        </div>
                        {!isQuickView && (
                          <div className="action-buttons">
                            {Helpers.getPermission(Helpers.permissionEntities?.GOOD_RECEIVING,
                              Helpers.permissionTypes.CREATE, user) && selectedGRN?.status.toUpperCase() === 'DRAFT'
                              && (
                                <Popconfirm
                                  placement="topRight"
                                  title="Are you sure you want to issue this GRN?"
                                  onConfirm={() => {
                                    this.setState({ currentUpdate: 'ISSUED' });
                                    updateGRNStatus({
                                      grn_id: selectedGRN?.grn_id,
                                      status: 'ISSUED',
                                    }, () => {
                                      this.setState({ currentUpdate: '' });
                                      if (user?.tenant_info?.seller_rating_config?.is_active) {
                                        this.setState({ showModal: true });
                                      } else {
                                        getGRNById(Helpers.getTenantEntityPermission(user?.user_tenants, Helpers.permissionEntities.GOOD_RECEIVING, Helpers.permissionTypes.READ).join(','), selectedGrnId || match?.params?.grnId);
                                        if (callback) callback();
                                      }
                                    });
                                  }}
                                  okText="Yes"
                                  cancelText="No"
                                >
                                  <H3Text
                                    text={(
                                      <div>
                                        {(currentUpdate === 'ISSUED' && updateGRNStatusLoading)
                                          ? <LoadingOutlined /> : (
                                            <span>Issue GRN</span>
                                          )}
                                      </div>
                                    )}
                                    className="action-button action-button-big"
                                  />
                                </Popconfirm>
                              )}

                            {Helpers.getPermission(Helpers.permissionEntities.GOOD_RECEIVING,
                              Helpers.permissionTypes.CREATE, user) && !selectedGRN?.grn_payments?.length
                              && selectedGRN?.status.toUpperCase() !== 'VOID' && selectedGRN?.status.toUpperCase() !== 'REJECTED'
                              && (
                                <Popconfirm
                                  placement="topRight"
                                  title="Are you sure you want to cancel this GRN?"
                                  onConfirm={() => {
                                    this.setState({ currentUpdate: 'VOID' });
                                    updateGRNStatus({
                                      grn_id: selectedGRN?.grn_id,
                                      grn_tenant_id: selectedGRN?.grn_tenant_id,
                                      grn_entity_type: 'PURCHASE_ORDER',
                                      status: 'VOID',
                                    }, () => {
                                      this.setState({ currentUpdate: '' });
                                      getGRNById(Helpers.getTenantEntityPermission(user?.user_tenants, Helpers.permissionEntities.GOOD_RECEIVING, Helpers.permissionTypes.READ).join(','), selectedGrnId || match?.params?.grnId);
                                      if (callback) callback();
                                    });
                                  }}
                                  okText="Yes"
                                  cancelText="No"
                                >
                                  <H3Text
                                    text={(
                                      <div>
                                        {(currentUpdate === 'VOID' && updateGRNStatusLoading)
                                          ? <LoadingOutlined /> : (
                                            <span>Cancel</span>
                                          )}
                                      </div>
                                    )}
                                    className="action-button action-button-big"
                                  />
                                </Popconfirm>
                              )}
                            {Helpers.getPermission(Helpers.permissionEntities.GOOD_RECEIVING,
                              Helpers.permissionTypes.DELETE, user) && !selectedGRN?.grn_payments?.length
                              && selectedGRN?.status.toUpperCase() === 'VOID'
                              && (
                                <Popconfirm
                                  placement="topRight"
                                  title="Are you sure you want to delete this GRN permanently?"
                                  onConfirm={() => {
                                    this.setState({ currentUpdate: 'DELETE' });
                                    deleteGrn(selectedGRN?.grn_tenant_id, selectedGRN?.grn_id, () => {
                                      this.setState({ currentUpdate: '' });
                                      if (callback) { callback(); } else {
                                        history.push('/purchase/goods-receiving/');
                                      }
                                    });
                                  }}
                                  okText="Yes"
                                  cancelText="No"
                                >
                                  <H3Text
                                    text={(
                                      <div>
                                        {(currentUpdate === 'DELETE' && deleteGrnLoading) ? <LoadingOutlined /> : (
                                          <span>Delete</span>
                                        )}
                                      </div>
                                    )}
                                    className="action-button action-button-big"
                                  />
                                </Popconfirm>
                              )}

                            {user?.user_tenants?.find((item) => item?.tenant_id === selectedGRN?.tenant_info?.tenant_id)?.zoho_branch_id && selectedGRN?.seller_id && ['ISSUED', 'VOID'].includes(selectedGRN?.status) && !isApInvoiceEnabled && (
                              <Popconfirm
                                placement="topRight"
                                title="Sync with Zoho Books?"
                                onConfirm={() => {
                                  if (!syncGRNLoading) {
                                    syncGRN({ grn_id: selectedGRN?.grn_id },
                                      () => getGRNById(Helpers.getTenantEntityPermission(user?.user_tenants, Helpers.permissionEntities.GOOD_RECEIVING, Helpers.permissionTypes.READ).join(','), selectedGrnId || match?.params?.grnId));
                                  }
                                }}
                                okText="Yes"
                                cancelText="No"
                              >
                                <div className="action-button action-button-big">
                                  <H3Image src={zohoIcon} className="action-menu-button-icon-zoho" />
                                  &nbsp;
                                  <H3Text text="Sync" />
                                </div>
                              </Popconfirm>
                            )}

                            {selectedGRN?.status === 'ISSUED' && window.screen.width > 425 && (
                              <Dropdown
                                overlay={(
                                  <Menu>
                                    {Helpers.getPermission(Helpers.permissionEntities.DEBIT_NOTE,
                                      Helpers.permissionTypes.CREATE, user) && selectedGRN?.status.toUpperCase() === 'ISSUED' && window.screen.width > 425 && !isApInvoiceEnabled && (
                                        <Menu.Item>
                                          <H3Text
                                            text={(
                                              <div style={{ display: 'flex', alignItems: 'center' }}>
                                                <div>
                                                  + Debit Note
                                                </div>
                                                {!user?.tenant_info?.purchase_config?.sub_modules?.debit_note?.is_active && (
                                                  <Popconfirm
                                                    placement="topRight"
                                                    title="This feature is not accessible within your current plan to use this feature contact us."
                                                    onConfirm={() => window.Intercom('showNewMessage')}
                                                    okText="Contact Us"
                                                    cancelText="Cancel"
                                                  >
                                                    <img className="barcode-restrict" style={{ marginLeft: '4px' }} src={Crown} alt="premium" />
                                                  </Popconfirm>
                                                )}
                                              </div>
                                            )}
                                            onClick={() => {
                                              if (user?.tenant_info?.purchase_config?.sub_modules?.debit_note?.is_active) {
                                                this.setState({ showDnForm: true });
                                              }
                                            }}
                                            className="action-menu-button"
                                          />
                                        </Menu.Item>
                                      )}
                                    {Helpers.getPermission(Helpers.permissionEntities.VENDOR_PAYOUT,
                                      Helpers.permissionTypes.CREATE, user) && selectedGRN?.status.toUpperCase() === 'ISSUED' && (selectedGRN?.grn_grand_total - selectedGRN?.total_payment_made) > 0 && !isApInvoiceEnabled
                                      && (
                                        <Menu.Item>
                                          <H3Text
                                            text={(
                                              <div style={{ display: 'flex', alignItems: 'center' }}>
                                                <div>
                                                  Record Payment
                                                </div>
                                                {!user?.tenant_info?.purchase_config?.sub_modules?.payment_outgoing?.is_active && (
                                                  <Popconfirm
                                                    placement="topRight"
                                                    title="This feature is not accessible within your current plan to use this feature contact us."
                                                    onConfirm={() => window.Intercom('showNewMessage')}
                                                    okText="Contact Us"
                                                    cancelText="Cancel"
                                                  >
                                                    <img className="barcode-restrict" style={{ marginLeft: '4px' }} src={Crown} alt="premium" />
                                                  </Popconfirm>
                                                )}
                                              </div>
                                            )}
                                            onClick={
                                              () => this.setState({ showPayment: true })
                                            }
                                            className="action-menu-button"
                                          />
                                        </Menu.Item>
                                      )}
                                    {Helpers.getPermission(Helpers.permissionEntities.VENDOR_PAYOUT,
                                      Helpers.permissionTypes.CREATE, user) && selectedGRN?.status.toUpperCase() === 'ISSUED' && (selectedGRN?.grn_grand_total - selectedGRN?.total_payment_made) > 0
                                      && window.screen.width > 425 && !isApInvoiceEnabled && (
                                        <Menu.Item>
                                          <H3Text
                                            text={(
                                              <div style={{ display: 'flex', alignItems: 'center' }}>
                                                <div>
                                                  Link Payment
                                                </div>
                                                {!user?.tenant_info?.purchase_config?.sub_modules?.payment_outgoing?.is_active && (
                                                  <Popconfirm
                                                    placement="topRight"
                                                    title="This feature is not accessible within your current plan to use this feature contact us."
                                                    onConfirm={() => window.Intercom('showNewMessage')}
                                                    okText="Contact Us"
                                                    cancelText="Cancel"
                                                  >
                                                    <img className="barcode-restrict" style={{ marginLeft: '4px' }} src={Crown} alt="premium" />
                                                  </Popconfirm>
                                                )}
                                              </div>
                                            )}
                                            onClick={() => this.setState({ linkPayment: true })}
                                            className="action-menu-button"
                                          />
                                        </Menu.Item>
                                      )}
                                    {['ISSUED'].includes(selectedGRN?.status) && (selectedGRN?.grn_quality_checks) && (
                                      <Menu.Item>
                                        <div
                                          className="action-menu-button-inline"
                                          onClick={() => {
                                            getDownloadQualityChecks(selectedGRN?.grn_tenant_id, '', 'GOOD_RECEIVING_NOTE', selectedGRN?.grn_id), () => {
                                              getGRNById(Helpers.getTenantEntityPermission(user?.user_tenants, Helpers.permissionEntities.GOOD_RECEIVING, Helpers.permissionTypes.READ).join(','), selectedGrnId || match?.params?.grnId);
                                            };
                                          }}
                                        >
                                          <H3Text
                                            text="Download QC Report"
                                            className="action-menu-button"
                                          />
                                        </div>
                                      </Menu.Item>
                                    )}
                                    {isTallyConnected && !isApInvoiceEnabled && (
                                      <Menu.Item>
                                        <div
                                          className="action-menu-button-inline"
                                          onClick={() => {
                                            if (user?.tenant_info?.integration_config?.sub_modules?.tally?.is_active) {
                                              syncTallyGRN({ grn_id: selectedGrnId || match?.params?.grnId },
                                                () => {
                                                  if (selectedGRN?.tally_updated_at) {
                                                    pullTallyVendorPayment({ seller_id: selectedGRN?.seller_id, tenant_id: selectedGRN?.tenant_info?.tenant_id });
                                                  }
                                                  getGRNById(Helpers.getTenantEntityPermission(user?.user_tenants, Helpers.permissionEntities.GOOD_RECEIVING, Helpers.permissionTypes.READ).join(','), selectedGrnId || match?.params?.grnId);
                                                });
                                            }
                                          }}
                                        >
                                          <H3Image src={TallyPrime} className="action-menu-button-icon" />
                                          <H3Text
                                            text={(
                                              <div style={{ display: 'flex', alignItems: 'center' }}>
                                                <div>
                                                  Push with Tally
                                                </div>
                                                {!user?.tenant_info?.integration_config?.sub_modules?.tally?.is_active && (
                                                  <Popconfirm
                                                    placement="topRight"
                                                    title="This feature is not accessible within your current plan to use this feature contact us."
                                                    onConfirm={() => window.Intercom('showNewMessage')}
                                                    okText="Contact Us"
                                                    cancelText="Cancel"
                                                  >
                                                    <img className="barcode-restrict" style={{ marginLeft: '4px' }} src={Crown} alt="premium" />
                                                  </Popconfirm>
                                                )}
                                              </div>
                                            )}
                                            className="action-menu-button"
                                          />
                                        </div>
                                      </Menu.Item>
                                    )}

                                    {selectedGRN?.integration_busy_config?.status === 'CONNECTED' && ['ISSUED'].includes(selectedGRN?.status) && !isApInvoiceEnabled && (
                                      <Menu.Item>
                                        <div
                                          className="action-menu-button-inline"
                                          onClick={() => {
                                            if (user?.tenant_info?.integration_config?.sub_modules?.busy?.is_active) {
                                              pushGrnToBusy({
                                                grn_id: selectedGRN?.grn_id,
                                              }, () => {
                                                getGRNById(Helpers.getTenantEntityPermission(user?.user_tenants, Helpers.permissionEntities.GOOD_RECEIVING, Helpers.permissionTypes.READ).join(','), selectedGrnId || match?.params?.grnId);
                                              });
                                            }
                                          }}
                                        >
                                          <H3Image
                                            src={Busy}
                                            className="action-menu-button-icon"
                                            style={{
                                              borderRadius: '20%',
                                            }}
                                          />
                                          <H3Text
                                            text={(
                                              <div style={{ display: 'flex', alignItems: 'center' }}>
                                                <div>
                                                  Push To Busy
                                                </div>
                                                {!user?.tenant_info?.integration_config?.sub_modules?.tally?.is_active && (
                                                  <Popconfirm
                                                    placement="topRight"
                                                    title="This feature is not accessible within your current plan to use this feature contact us."
                                                    onConfirm={() => window.Intercom('showNewMessage')}
                                                    okText="Contact Us"
                                                    cancelText="Cancel"
                                                  >
                                                    <img className="barcode-restrict" style={{ marginLeft: '4px' }} src={Crown} alt="premium" />
                                                  </Popconfirm>
                                                )}
                                              </div>
                                            )}
                                            className="action-menu-button"
                                          />
                                        </div>
                                      </Menu.Item>
                                    )}
                                    {Helpers.getPermission(Helpers.permissionEntities.ACCOUNT_PAYABLE_INVOICE,
                                      Helpers.permissionTypes.CREATE, user) && selectedGRN?.status.toUpperCase() === 'ISSUED' && window.screen.width > 425 && isApInvoiceEnabled
                                      && (
                                        <Menu.Item>
                                          <H3Text
                                            text={(
                                              <div style={{ display: 'flex', alignItems: 'center' }}>
                                                <div>
                                                  + AP Invoice
                                                </div>
                                                {!user?.tenant_info?.purchase_config?.sub_modules?.account_payable_invoice?.is_active && (
                                                  <Popconfirm
                                                    placement="topRight"
                                                    title="This feature is not accessible within your current plan to use this feature contact us."
                                                    onConfirm={() => window.Intercom('showNewMessage')}
                                                    okText="Contact Us"
                                                    cancelText="Cancel"
                                                  >
                                                    <img className="barcode-restrict" style={{ marginLeft: '4px' }} src={Crown} alt="premium" />
                                                  </Popconfirm>
                                                )}
                                              </div>
                                            )}
                                            onClick={() => {
                                              if (user?.tenant_info?.purchase_config?.sub_modules?.account_payable_invoice?.is_active) {
                                                this.setState({ showApInvoiceForm: true });
                                              }
                                            }}
                                            className="action-menu-button"
                                          />
                                        </Menu.Item>
                                      )}
                                  </Menu>
                                )}
                                getPopupContainer={(triggerNode) => triggerNode.parentNode}
                                trigger={['click']}
                                overlayStyle={{ width: '150px' }}
                                placement="topLeft"
                              >
                                <div className="action-button action-button-big action-button-more no-right-border" onClick={(e) => e.preventDefault()}>
                                  More
                                  <CaretDownOutlined />
                                </div>
                              </Dropdown>
                            )}
                          </div>
                        )}
                      </div>
                    )}
                  </div> */}
                  <div className="document-header">
                    <div className="ant-row">
                      {selectedGRN?.tenant_seller_info?.seller_name && (
                        <div className="ant-col-md-24">
                          <H3Text
                            text={(
                              <span>
                                <UserOutlined />
                                {' '}
                                <span>
                                  <Link
                                    to={`/vendors/view/${selectedGRN?.seller_id}`}
                                    target="_blank"
                                  >
                                    {`${selectedGRN?.internal_slr_code} - ${selectedGRN?.tenant_seller_info?.seller_name}`}
                                  </Link>
                                </span>
                              </span>
                            )}
                            className="document-header__party-name"
                          />
                        </div>
                      )}
                      <div className="ant-col-md-12">
                        <Tooltip title={selectedGRN?.tenant_info?.tenant_name}>
                          <div className="document-header__field">
                            <H3Text text="Location" className="document-header__field-name" />
                            <H3Text text={selectedGRN?.tenant_info?.tenant_name?.length > 25 ? `${selectedGRN?.tenant_info?.tenant_name?.substring(0, 25)}..` : selectedGRN?.tenant_info?.tenant_name} className="document-header__field-value" />
                          </div>
                        </Tooltip>
                      </div>
                      <div className="ant-col-md-12">
                        <Tooltip title={selectedGRN?.tenant_department_info?.department_name}>
                          <div className="document-header__field">
                            <H3Text text="Department" className="document-header__field-name" />
                            <H3Text text={selectedGRN?.tenant_department_info?.department_name?.length > 25 ? `${selectedGRN?.tenant_department_info?.department_name?.substring(0, 25)}..` : selectedGRN?.tenant_department_info?.department_name} className="document-header__field-value" />
                          </div>
                        </Tooltip>
                      </div>
                      {selectedGRN?.goods_received_from && (
                        <div className="ant-col-md-12">
                          <div className="document-header__field">
                            <H3Text text="Transfer From" className="document-header__field-name" />
                            <H3Text
                              text={selectedGRN?.goods_received_from}
                              className="document-header__field-value"
                            />
                          </div>
                        </div>
                      )}
                      <div className="ant-col-md-12">
                        <div className="document-header__field">
                          <H3Text text="GRN Date" className="document-header__field-name" />
                          <H3Text
                            text={selectedGRN?.grn_date_time ? (toISTDate(selectedGRN?.grn_date_time).format('DD/MM/YYYY')) : '-'}
                            className="document-header__field-value"
                          />
                        </div>
                      </div>
                      {selectedGRN?.grn_entity_type === 'PURCHASE_ORDER' && (
                        <div className="ant-col-md-12">
                          <div className="document-header__field">
                            <H3Text text="Purchase Order#" className="document-header__field-name" />
                            {selectedGRN?.linked_pos?.length === 1 ? (
                              <H3Text
                                text={<Link to={`/approval?type=po&id=${selectedGRN?.linked_pos?.[0]?.po_id}`} target="_blank">{selectedGRN?.linked_pos?.[0].po_number}</Link>}
                                className="document-header__field-value"
                              />) : (
                              (() => {
                                const firstPos = selectedGRN?.linked_pos?.[0];
                                const remainingPos = selectedGRN?.linked_pos?.slice(1) || [];
                                const remainingCount = remainingPos.length;
                                return firstPos ? (
                                  <Tooltip
                                    title={
                                      remainingCount > 0 ? (
                                        <div className="tally-locations-tooltip">
                                          {remainingPos?.map((po, index) => (
                                            <Link
                                              to={`/approval?type=po&id=${po?.po_id}`}
                                              target="_blank"
                                              style={{
                                                fontWeight: 600,
                                                color: '#1890ff',
                                                fontSize: '13px',
                                                display: 'block',
                                                marginBottom: '4px',
                                              }}
                                            >
                                              {po?.po_number}
                                            </Link>
                                          ))}
                                        </div>
                                      ) : (
                                        <div className="tally-locations-tooltip">No additional Pos</div>
                                      )
                                    }
                                    placement="top"
                                    overlayInnerStyle={{ background: 'white', color: 'black' }}
                                  >
                                    <span >
                                      <Link
                                        to={`/approval?type=po&id=${firstPos.po_id}`}
                                        target="_blank"
                                        className="document-header__field-value"
                                      >
                                        {firstPos.po_number}
                                      </Link>
                                      <span
                                        style={{
                                          color: "gray"
                                        }}
                                      >
                                        {remainingCount > 0 && ` +${remainingCount} more`}
                                      </span>
                                    </span>
                                  </Tooltip>
                                ) : (
                                  <span>-</span>
                                );
                              })()
                            )}
                          </div>
                        </div>
                      )}
                      {selectedGRN?.invoice_date && (
                        <div className="ant-col-md-12">
                          <div className="document-header__field document-header__date">
                            <H3Text text="Invoice Date" className="document-header__field-name" />
                            {quickUpdateDisable ? (
                              <div className="document-header__field-input">
                                <DatePicker
                                  value={updateInvoiceDate}
                                  onChange={(value) => {
                                    if (!user?.tenant_info?.purchase_config?.sub_modules?.goods_receiving_note?.settings?.allow_grn_date_before_invoice_date && dayjs(value).isBefore(dayjs(selectedGRN?.grn_date_time))) {
                                      notification.error({
                                        message: "Invoice date cannot be earlier than GRN Date.",
                                        placement: "top",
                                        duration: "4"
                                      })
                                    } else {
                                      this.setState({ updateInvoiceDate: value });
                                      quickUpdateGRN({
                                        invoice_date: value,
                                        grn_id: selectedGRN.grn_id,
                                      });
                                    }
                                  }}
                                  format="DD-MMM-YYYY"
                                  allowClear={false}
                                />
                              </div>
                            ) : (
                              <H3Text
                                text={selectedGRN?.invoice_date ? (toISTDate(selectedGRN?.invoice_date).format('DD/MM/YYYY')) : '-'}
                                className="document-header__field-value"
                              />
                            )}
                          </div>
                        </div>
                      )}
                      {selectedGRN?.conversion_rate && (
                        <div className="ant-col-md-12">
                          <div className="document-header__field">
                            <H3Text text="Conversion Rate" className="document-header__field-name" />
                            <H3Text text={`${selectedGRN?.conversion_rate}`} className="document-header__field-value" />
                          </div>
                        </div>
                      )}
                      {selectedGRN?.tenant_seller_info?.gst_number && (
                        <div className="ant-col-md-12">
                          <div className="document-header__field">
                            <H3Text text="GSTIN" className="document-header__field-name" />
                            {(isGstNumberEditable) && (
                              <div className="document-header__field-input ">
                                <input
                                  value={updatedGstNumber}
                                  onChange={(e) => {
                                    this.setState({ updatedGstNumber: e.target.value });
                                  }}
                                  onKeyPress={(e) => {
                                    if (e.key === 'Enter') {
                                      this.setState({ isGstNumberEditable: false });
                                      quickUpdateGRN({
                                        gst_number: updatedGstNumber || null,
                                        grn_id: selectedGRN.grn_id,
                                      });
                                    }
                                  }}
                                  className="document-custom-input"
                                  maxLength="15"
                                />
                                &nbsp;
                                <CheckOutlined
                                  style={{ color: '#1890ff', cursor: 'pointer' }}
                                  onClick={() => {
                                    this.setState({ isGstNumberEditable: false });
                                    quickUpdateGRN({
                                      gst_number: updatedGstNumber || null,
                                      grn_id: selectedGRN.grn_id,
                                    });
                                  }}
                                />
                              </div>
                            )}
                            {(!isGstNumberEditable) && (
                              <div className="document-header__field-value">
                                {updatedGstNumber && (
                                  <H3Text
                                    text={updatedGstNumber}
                                  />
                                )}
                                {(quickUpdateDisable && !accountingGSTTransactionAsPerMaster) && (
                                  <EditFilled
                                    className="edit-icon"
                                    onClick={() => {
                                      this.setState({ isGstNumberEditable: true });
                                    }}
                                  />
                                )}
                              </div>
                            )}
                          </div>
                        </div>
                      )}
                      {selectedGRN?.invoice_number && (
                        <div className="ant-col-md-12">
                          <div className="document-header__field">
                            <H3Text text="Vendor Invoice#" className="document-header__field-name" />
                            {(isInvoiceNumber) && (
                              <div className="document-header__field-input">
                                <input
                                  value={updateInvoiceNumber}
                                  onChange={(e) => {
                                    this.setState({ updateInvoiceNumber: e.target.value });
                                  }}
                                  onKeyPress={(e) => {
                                    if (e.key === 'Enter') {
                                      this.setState({ isInvoiceNumber: false });
                                      quickUpdateGRN({
                                        invoice_number: updateInvoiceNumber,
                                        grn_id: selectedGRN.grn_id,
                                      }, () => {
                                        this.setState({ isUserReadyForPrefillData: true });
                                      }, () => {
                                        this.setState({ isUserReadyForPrefillData: false });
                                      });
                                    }
                                  }}
                                  className="document-custom-input"
                                />
                                &nbsp;
                                <CheckOutlined
                                  style={{ color: '#1890ff', cursor: 'pointer' }}
                                  onClick={() => {
                                    this.setState({ isInvoiceNumber: false });
                                    quickUpdateGRN({
                                      invoice_number: updateInvoiceNumber || null,
                                      grn_id: selectedGRN.grn_id,
                                    }, () => {
                                      this.setState({ isUserReadyForPrefillData: true });
                                    }, () => {
                                      this.setState({ isUserReadyForPrefillData: false });
                                    });
                                  }}
                                />
                              </div>
                            )}
                            {(!isInvoiceNumber) && (
                              <div className="document-header__field-value">
                                {updateInvoiceNumber && (
                                  <H3Text text={selectedGRN?.invoice_number} />
                                )}
                                {quickUpdateDisable && (
                                  <EditFilled
                                    className="edit-icon"
                                    onClick={() => {
                                      this.setState({ isInvoiceNumber: true });
                                    }}
                                  />
                                )}
                              </div>
                            )}
                          </div>
                        </div>
                      )}

                      <div className="ant-col-md-12">
                        <div className="document-header__field">
                          <H3Text text="e-Way Bill#" className="document-header__field-name" />

                          {(isEWayBillNumber) && (
                            <div className="document-header__field-value">
                              <input
                                value={updateEWayBillNumber}
                                onChange={(e) => {
                                  this.setState({ updateEWayBillNumber: e.target.value });
                                }}
                                onKeyPress={(e) => {
                                  if (e.key === 'Enter') {
                                    this.setState({ isEWayBillNumber: false });
                                    quickUpdateGRN({
                                      e_way_bill_number: updateEWayBillNumber,
                                      grn_id: selectedGRN.grn_id,
                                    }, () => {
                                      getActivityLog(selectedGRN?.grn_id, 'good_receiving_note');
                                      this.setState({ isUserReadyForPrefillData: true });
                                    }, () => {
                                      this.setState({ isUserReadyForPrefillData: false });
                                    });
                                  }
                                }}
                                className="document-custom-input"
                              />
                              &nbsp;
                              <CheckOutlined
                                style={{ color: '#1890ff', cursor: 'pointer' }}
                                onClick={() => {
                                  this.setState({ isEWayBillNumber: false });
                                  quickUpdateGRN({
                                    e_way_bill_number: updateEWayBillNumber || null,
                                    grn_id: selectedGRN.grn_id,
                                  }, () => {
                                    getActivityLog(selectedGRN?.grn_id, 'good_receiving_note');
                                    this.setState({ isUserReadyForPrefillData: true });
                                  }, () => {
                                    this.setState({ isUserReadyForPrefillData: false });
                                  });
                                }}
                              />
                            </div>
                          )}
                          {(!isEWayBillNumber) && (
                            <div className="document-header__field-value">
                              {updateEWayBillNumber && (
                                <H3Text text={selectedGRN?.e_way_bill_number} />
                              )}
                              {quickUpdateDisable && (
                                <EditFilled
                                  className="edit-icon"
                                  onClick={() => {
                                    this.setState({ isEWayBillNumber: true });
                                  }}
                                />
                              )}
                            </div>
                          )}
                        </div>
                      </div>
                      <div className="ant-col-md-12">
                        <div className="document-header__field document-header__date">
                          <H3Text text="Due Date" className="document-header__field-name" />

                          {quickUpdateDisable ? (
                            <div className="document-header__field-input">
                              <DatePicker
                                value={updateDueDate}
                                onChange={(value) => {
                                  this.setState({ updateDueDate: value });
                                  quickUpdateGRN({
                                    grn_due_date: value,
                                    grn_id: selectedGRN.grn_id,
                                  });
                                }}
                                format="DD-MMM-YYYY"
                                allowClear={false}
                              />
                            </div>
                          ) : (
                            <H3Text
                              text={selectedGRN?.grn_due_date ? (toISTDate(selectedGRN?.grn_due_date).format('DD/MM/YYYY')) : '-'}
                              className="document-header__field-value"
                            />
                          )}
                        </div>
                      </div>
                      {
                        selectedGRN && (
                          <div className="ant-col-md-12">
                            <div className="document-header__field">
                              <H3Text text="e-Way Bill" className="document-header__field-name" />
                              {(isEWayBillAttachment) && (
                                <div className="document-header__field-value ">
                                  <input
                                    type="file"
                                    ref={this.fileInputRef}
                                    onChange={this.handleFileChangeEWayBill}
                                    className="document-custom-input"
                                  />
                                  &nbsp;
                                  <CheckOutlined
                                    style={{ color: '#1890ff', cursor: 'pointer' }}
                                    onClick={() => {
                                      this.setState({ isEWayBillAttachment: false });
                                      const payload = {
                                        e_way_bill: [{
                                          url: eWayBillAttachment?.response?.location,
                                          type: eWayBillAttachment?.response?.contentType,
                                          name: eWayBillAttachment?.response?.originalname,
                                        }],
                                        grn_id: selectedGRN.grn_id,
                                      };
                                      quickUpdateGRN(payload, () => {
                                        getActivityLog(selectedGRN?.grn_id, 'good_receiving_note');
                                        this.setState({ isUserReadyForPrefillData: true });
                                      }, () => {
                                        this.setState({ isUserReadyForPrefillData: false });
                                      });
                                    }}
                                  />
                                </div>
                              )}
                              {(!isEWayBillAttachment) && (
                                <div className="document-header__field-value">
                                  <H3Text
                                    text={<a href={selectedGRN?.e_way_bill?.[0]?.url} target="_blank" rel="noreferrer">{selectedGRN?.e_way_bill?.[0]?.name}</a>}
                                    style={{
                                      whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis', width: selectedGRN?.e_way_bill?.[0]?.name ? '90%' : '',
                                    }}
                                  />
                                  {quickUpdateDisable && (
                                    <EditFilled
                                      className="edit-icon"
                                      onClick={() => {
                                        this.setState({ isEWayBillAttachment: true });
                                      }}
                                    />
                                  )}
                                </div>
                              )}
                            </div>
                          </div>
                        )
                      }
                      {user?.tenant_info?.seller_rating_config?.is_active && (
                        <div className="ant-col-md-12">
                          <div
                            className="document-header__field"
                            onMouseEnter={() => {
                              document.getElementById('editIcon').style.display = 'block';
                            }}
                            onMouseLeave={() => {
                              document.getElementById('editIcon').style.display = 'none';
                            }}
                          >
                            <H3Text text="GRN Rating" className="document-header__field-name" />
                            <H3Text
                              text={(
                                <div style={{ display: 'flex', cursor: 'pointer', alignItems: 'center' }} onClick={() => this.setState({ showModal: true, isUpdateRating: true })}>
                                  <div className={`rating-container-update ${getRatingColor(selectedGRN?.rating_info?.rating)}`} style={{ width: selectedGRN?.rating_info?.rating > 0 ? '40px' : '85px' }}>
                                    <div className="rating-container-main">
                                      <span className="rating-value">{selectedGRN?.rating_info?.rating > 0 ? selectedGRN?.rating_info?.rating?.toFixed(1) : 'Rate Now'}</span>
                                      <span className="rating-star">&#9733;</span>
                                    </div>
                                  </div>
                                  <div style={{ margin: '2px 0px 0px 4px' }}>
                                    <EditOutlined
                                      id="editIcon"
                                    />
                                  </div>
                                </div>
                              )}
                              className="document-header__field-value"
                            />
                          </div>
                        </div>
                      )}
                      {selectedGRN?.integration_busy_config?.status === 'CONNECTED' && !isApInvoiceEnabled && (
                        <div className="ant-col-md-12">
                          <div className="document-header__field">
                            <H3Text text="Busy STPT Account" className="document-header__field-name" />
                            <div className="document-header__field-input">
                              <PRZSelect
                                showSearch
                                placeholder="select account"
                                value={selectedGRN?.busy_stpt_name}
                                options={selectedGRN?.integration_busy_config?.purchase_type_master_list.map((__k) => ({
                                  value: __k.name,
                                  label: __k.name,
                                }))}
                                loading={quickUpdateGRNLoading}
                                size="small"
                                optionFilterProp="children"
                                filterOption={(input, option) => (option?.label ?? '').toLowerCase().includes(input.toLowerCase())}
                                onChange={(value) => {
                                  quickUpdateGRN({
                                    busy_stpt_name: value,
                                    grn_id: selectedGRN.grn_id,
                                  }, () => getActivityLog(selectedGRN?.grn_id, 'good_receiving_note'));
                                }}
                                disabled={!quickUpdateDisable}
                              />
                            </div>
                          </div>
                        </div>
                      )}
                      {selectedGRN?.it_id && selectedGRN?.integration_tally_config?.purchase_account_selection === 'FROM_GRN' && !isApInvoiceEnabled && (
                        <div className="ant-col-md-12">
                          <div className="document-header__field">
                            <H3Text text="Tally purchase Account" className="document-header__field-name" />
                            <div className="document-header__field-input">
                              <PRZSelect
                                showSearch
                                placeholder="select account"
                                value={selectedGRN?.tally_purchase_account}
                                options={selectedGRN?.integration_tally_config?.purchase_account_list?.map((__k) => ({
                                  value: __k.purchase_account_name,
                                  label: __k.purchase_account_name,
                                }))}
                                loading={quickUpdateGRNLoading}
                                size="small"
                                optionFilterProp="children"
                                filterOption={(input, option) => (option?.label ?? '').toLowerCase().includes(input.toLowerCase())}
                                onChange={(value) => {
                                  quickUpdateGRN({
                                    tally_purchase_account: value,
                                    grn_id: selectedGRN.grn_id,
                                  }, () => getActivityLog(selectedGRN?.grn_id, 'good_receiving_note'));
                                }}
                                disabled={!quickUpdateDisable}
                              />
                            </div>
                          </div>
                        </div>
                      )}
                      {selectedGRN?.it_id && selectedGRN?.integration_tally_config?.purchase_voucher_type_selection === 'DOC_LEVEL' && !isApInvoiceEnabled && (
                        <div className="ant-col-md-12">
                          <div className="document-header__field">
                            <H3Text text="Tally Voucher Type" className="document-header__field-name" />
                            <div className="document-header__field-input">
                              <PRZSelect
                                showSearch
                                placeholder="select account"
                                value={selectedGRN?.purchase_voucher_type}
                                options={selectedGRN?.integration_tally_config?.voucher_types?.map((__k) => ({
                                  value: __k.voucher_type_name,
                                  label: __k.voucher_type_name,
                                }))}
                                loading={quickUpdateGRNLoading}
                                size="small"
                                optionFilterProp="children"
                                filterOption={(input, option) => (option?.label ?? '').toLowerCase().includes(input.toLowerCase())}
                                onChange={(value) => {
                                  quickUpdateGRN({
                                    purchase_voucher_type: value,
                                    grn_id: selectedGRN.grn_id,
                                  }, () => getActivityLog(selectedGRN?.grn_id, 'good_receiving_note'));
                                }}
                                disabled={!quickUpdateDisable}
                              />
                            </div>
                          </div>
                        </div>
                      )}
                      {
                        user?.tenant_info?.zoho_branch_id && selectedGRN?.seller_id && selectedGRN?.zoho_bill_id && !isApInvoiceEnabled && (
                          <div className="ant-col-md-12">
                            <div className="document-header__field">
                              <H3Text text="Zoho Status" className="document-header__field-name" />
                              <H3Text
                                text={(
                                  <a href={`https://books.zoho.in/app#/bills/${selectedGRN?.zoho_bill_id}`} target="_blank" rel="noreferrer">
                                    <span className="status-tag">Pushed</span>
                                    <LinkOutlined />
                                  </a>
                                )}
                                className="document-header__field-value"
                              />
                            </div>
                          </div>
                        )
                      }
                      {
                        isTallyConnected && !isApInvoiceEnabled && (
                          <div className="ant-col-md-12">
                            <div className="document-header__field">
                              <H3Text text="Tally Status" className="document-header__field-name" />
                              {(selectedGRN?.tally_updated_at && !selectedGRN?.tally_warning_message) && (
                                <H3Text
                                  text={(
                                    <React.Fragment>
                                      <span className="status-tag">Pushed</span>
                                    </React.Fragment>
                                  )}
                                  className="document-header__field-value"
                                />
                              )}
                              {selectedGRN?.tally_warning_message && (
                                <H3Text
                                  text={(
                                    <React.Fragment>
                                      <span
                                        className="status-tag"
                                        style={{
                                          backgroundColor: 'rgb(255, 0, 0)',
                                        }}
                                      >
                                        Error
                                      </span>
                                      &nbsp;
                                      <Tooltip
                                        title={selectedGRN?.tally_warning_message}
                                      >
                                        <InfoCircleOutlined style={{ color: 'rgb(255, 0, 0)', cursor: 'pointer' }} />
                                      </Tooltip>
                                    </React.Fragment>
                                  )}
                                  className="document-header__field-value"
                                />
                              )}
                              {(!selectedGRN?.tally_voucher_id && !selectedGRN?.tally_warning_message && !selectedGRN?.tally_updated_at) && (
                                <H3Text
                                  text={(
                                    <React.Fragment>
                                      <span
                                        className="status-tag"
                                        style={{
                                          backgroundColor: 'rgb(70, 139, 247)',
                                        }}
                                      >
                                        Not Synced
                                      </span>
                                    </React.Fragment>
                                  )}
                                  className="document-header__field-value"
                                />
                              )}
                            </div>
                          </div>
                        )
                      }
                      {
                        selectedGRN?.integration_busy_config?.status === 'CONNECTED' && ['ISSUED'].includes(selectedGRN?.status) && !isApInvoiceEnabled && (
                          <div className="ant-col-md-12">
                            <div className="document-header__field">
                              <H3Text text="Busy Status" className="document-header__field-name" />
                              {selectedGRN?.busy_voucher_id && (
                                <H3Text
                                  text={(
                                    <React.Fragment>
                                      <span className="status-tag">Pushed</span>
                                    </React.Fragment>
                                  )}
                                  className="document-header__field-value"
                                />
                              )}
                              {selectedGRN?.busy_sync_error && (
                                <H3Text
                                  text={(
                                    <React.Fragment>
                                      <span
                                        className="status-tag"
                                        style={{
                                          backgroundColor: 'rgb(255, 0, 0)',
                                        }}
                                      >
                                        Error
                                      </span>
                                      &nbsp;
                                      <Tooltip
                                        title={selectedGRN?.busy_sync_error}
                                      >
                                        <InfoCircleOutlined style={{ color: 'rgb(255, 0, 0)', cursor: 'pointer' }} />
                                      </Tooltip>
                                    </React.Fragment>
                                  )}
                                  className="document-header__field-value"
                                />
                              )}
                              {(!selectedGRN?.busy_voucher_id && !selectedGRN?.busy_sync_error) && (
                                <H3Text
                                  text={(
                                    <React.Fragment>
                                      <span
                                        className="status-tag"
                                        style={{
                                          backgroundColor: 'rgb(70, 139, 247)',
                                        }}
                                      >
                                        Not Synced
                                      </span>
                                    </React.Fragment>
                                  )}
                                  className="document-header__field-value"
                                />
                              )}
                            </div>
                          </div>
                        )
                      }
                      {selectedGRN?.subcontractor_mos?.length > 0 && (
                        <div className="ant-col-md-12">
                          <div className="document-header__field">
                            <H3Text text="Sub Contractor MO" className="document-header__field-name" />
                            <H3Text
                              text={
                                selectedGRN?.subcontractor_mos?.length == 1 ? (
                                  <Link to={`/production/manufacturing-orders/view/${selectedGRN?.subcontractor_mos[0]?.mo_id}`} key={selectedGRN?.subcontractor_mos[0]?.mo_id} target='_blank' style={{ 'color': '#2D7DF7' }}>
                                    {selectedGRN?.subcontractor_mos[0]?.mo_number}
                                  </Link>
                                ) : (
                                  <Tooltip overlay={(
                                    <div
                                      style={{
                                        color: 'rgb(0, 0, 0)',
                                        fontSize: '12px',
                                        fontWeight: '500',
                                        padding: '2px',
                                      }}
                                    >
                                      {selectedGRN?.subcontractor_mos?.map(item => {
                                        <Link to={`/production/manufacturing-orders/view/${item?.mo_id}`} key={item?.mo_id} target='_blank' style={{ 'color': '#2D7DF7' }}>{item?.mo_number}</Link>
                                      })}
                                    </div>
                                  )}
                                    color="rgb(234, 242, 254)"
                                    overlayStyle={{ zIndex: 9999 }}>
                                    {`${selectedGRN?.subcontractor_mos[0]?.mo_number}... +${selectedGRN?.subcontractor_mos?.length - 1} more`}
                                  </Tooltip>
                                )
                              }
                              className="document-header__field-value"
                            />
                          </div>
                        </div>
                      )}
                      {selectedGRN?.custom_fields?.length > 0 ? (
                        <ViewCustomFields
                          customFields={selectedGRN?.custom_fields?.filter((cf) => cf.is_active)}
                          wrapperClass="document-header__field"
                          labelClass="document-header__field-name"
                          valueClass="document-header__field-value"
                          inputClassName="document-header__field-input"
                          isHorizontal
                          isEditable={quickUpdateDisable}
                          handleCustomFieldUpdate={quickUpdateGRN}
                          entityId={selectedGRN.grn_id}
                          callback={() => getGRNById(Helpers.getTenantEntityPermission(user?.user_tenants, Helpers.permissionEntities.GOOD_RECEIVING, Helpers.permissionTypes.READ).join(','), selectedGrnId || match?.params?.grnId)}
                          entityName="grn_id"
                          entityType="GOOD_RECEIVING_NOTES"
                        />
                      ) : ''}
                    </div>
                  </div>
                  <br />
                  <Tabs
                    activeKey={currentTab}
                    onChange={(key) => {
                      this.setState({ currentTab: key });
                    }}
                    mode="horizontal"
                    type="card"
                  >
                    <TabPane tab="Goods Received" key="/goods-received" />
                    {getQcData({ data: selectedGRN?.grn_lines, selectedGRN })?.length > 0 && Helpers.getPermission(Helpers.permissionEntities.QUALITY_CHECKS, Helpers.permissionTypes.READ, user) && (
                      <TabPane
                        tab={(
                          <div>
                            Quality Checks &nbsp;
                            {getQcData({ data: selectedGRN?.grn_lines, selectedGRN })?.filter((item) => item.status !== 'COMPLETED')?.length ? <Badge count={getQcData({ data: selectedGRN?.grn_lines, selectedGRN })?.filter((item) => item.status !== 'COMPLETED')?.length} /> : ''}
                          </div>
                        )}
                        key="/grn-quality-checks"
                      />
                    )}
                  </Tabs>
                  {currentTab === '/goods-received' && (
                    <Fragment>
                      <ViewGRNLines
                        selectedGRN={selectedGRN}
                        loading={createGRNLoading || syncGRNLoading || pullTallyVendorPaymentLoading || syncTallyGRNLoading}
                        user={user}
                        MONEY={MONEY}
                        selectedRowKeys={selectedRowKeys}
                        setState={(stateValue) => this.setState(stateValue)}
                        priceMasking={priceMasking}
                        isApInvoiceEnabled={isApInvoiceEnabled}
                        visibleColumns={visibleColumns}
                        cfGRNLine={cfGRNLine}
                      />
                      {/* <Table
                        bordered
                        showHeader
                        title={() => `Received Items (${selectedGRN?.grn_lines?.length})`}
                        loading={createGRNLoading || syncGRNLoading || pullTallyVendorPaymentLoading || syncTallyGRNLoading}
                        size="small"
                        columns={selectedGRN?.grn_entity_type === 'INVENTORY_TRANSFER' ? adjustmentColumns : this.getGRNColumns()}
                        dataSource={selectedGRN?.grn_lines?.map((item) => ({ ...item, key: uuidv4(), lineCustomFields: item?.grn_line_custom_fields })) || []}
                        pagination={false}
                        scroll={{ x: 'max-content' }}
                        rowKey="grn_line_id"
                        expandable={{
                          rowExpandable: (record) => record?.product_batches?.length,
                          expandedRowRender: (record) => (
                            <BatchesTable
                              title="Received Batches"
                              batches={record?.product_batches}
                              uomInfo={record?.uom_info?.[0]}
                              expiryDays={record?.product_sku_info?.expiry_days}
                              showCancel
                              productInfo={record?.product_sku_info}
                              hideCostPrice
                            />
                          ),
                        }}
                        rowSelection={(window.screen.width > 425 && user?.tenant_info?.global_config?.sub_modules?.barcoding?.is_active) ? {
                          onChange: (rowKeys, rows) => {
                            this.setState({ selectedRowKeys: rowKeys, selectedRows: rows });
                          },
                          selectedRowKeys,
                        } : false}
                      /> */}
                      <ViewGRNFooter
                        selectedGRN={selectedGRN}
                        user={user}
                        MONEY={MONEY}
                        fileList={fileList}
                        getAttachmentByIdLoading={getAttachmentByIdLoading}
                        updateAttachmentLoading={updateAttachmentLoading}
                        handleFileChange={(value) => handleFileChange({
                          fileListData: value,
                          selectedGRN,
                          updateAttachment,
                          getAttachmentById,
                          setState: (stateValue) => this.setState(stateValue),
                        })}
                        priceMasking={priceMasking}
                        isApInvoiceEnabled={isApInvoiceEnabled}
                      />
                      {/* <div className="view-document__totals-wrapper">
                        <div className="view__document-footer-left">
                          {selectedGRN?.remark && (
                            <div className="view__document-tc">
                              <H3Text text="Additional Remarks" className="view__document-tc-title" />
                              <H3Text
                                text={selectedGRN?.remark ? selectedGRN?.remark : ''}
                                className="view__document-tc-body"
                              />
                            </div>
                          )}
                          {!!selectedGRN?.narration && (
                            <div className="view__document-tc">
                              <H3Text text="Narration" className="view__document-tc-title" />
                              <H3Text
                                text={selectedGRN?.narration ? selectedGRN?.narration : ''}
                                className="view__document-tc-body"
                              />
                            </div>
                          )}
                          <Attachment
                            fileList={fileList}
                            disableCase={getAttachmentByIdLoading || updateAttachmentLoading}
                            labelClassName="orgFormLabel"
                            inputClassName=""
                            containerClassName="view__document-attachment"
                            handleFileChange={(value) => this.handleFileChange(value)}
                            updateEnable={Helpers.getPermission(Helpers.permissionEntities.GOOD_RECEIVING, Helpers.permissionTypes.UPDATE, user)}
                          />
                        </div>
                        {['PURCHASE_ORDER', 'GOOD_RECEIVING_NOTE'].includes(selectedGRN?.grn_entity_type) && (isDataMaskingPolicyEnable && isHideCostPrice) ? <HideComponent style={{
                          width: '40%',
                          marginLeft: 'auto',
                          marginTop: '10px'
                        }} /> : (
                          <div className={isApInvoiceEnabled ? "display-none" : "view-document__totals"}>
                            <div className="view-document__totals-field">
                              <H3Text text="Sub Total" className="view-document__totals-field-name" />
                              <H3Text text={MONEY((selectedGRN?.grn_base_price), selectedGRN?.org_currency_info?.currency_code)} className="view-document__totals-field-value" />
                            </div>
                            {selectedGRN?.discount_amount !== 0 && (
                              <div className="view-document__totals-field">
                                <H3Text
                                  text={`Discount ${!selectedGRN?.is_line_wise_discount
                                    ? `(${selectedGRN?.is_discount_in_percent
                                      ? `${selectedGRN?.discount_percentage}%`
                                      : `${MONEY(selectedGRN?.discount_amount, selectedGRN?.org_currency_info?.currency_code)}`
                                    })`
                                    : ''
                                    }`}
                                  className="view-document__totals-field-name"
                                />
                                {selectedGRN?.discount_amount > 0
                                  ? (
                                    <H3Text
                                      text={`(-) ${MONEY((selectedGRN?.discount_amount), selectedGRN?.org_currency_info?.currency_code)}`}
                                      className="view-document__totals-field-value danger-text"
                                    />
                                  )
                                  : (
                                    <H3Text
                                      text={MONEY((selectedGRN?.discount_amount), selectedGRN?.org_currency_info?.currency_code)}
                                      className="view-document__totals-field-value"
                                    />
                                  )}
                              </div>
                            )}
                            {this.renderFreight('top')}
                            {renderCharges(splitChargesData(selectedGRN?.other_charges).chargeWithTaxName)}
                            <div className="view-document__totals-field">
                              <H3Text text="Taxable Amount" className="view-document__totals-field-name" />
                              <H3Text text={MONEY((selectedGRN?.taxable_amount || 0), selectedGRN?.org_currency_info?.currency_code)} className="view-document__totals-field-value" />
                            </div>
                            {(selectedGRN?.tcs_id || selectedGRN?.tds_id) && !isVendorOverseas && (
                              <div className="view-document__totals-field">
                                <H3Text
                                  text={(
                                    <Fragment>
                                      {selectedGRN?.tcs_id ? 'TCS' : 'TDS'}
                                      &nbsp;
                                      <span className="table-subscript">{`${selectedGRN?.tcs_id ? 'tcs' : 'tds'}@${selectedGRN?.tcs_info?.tax_value || selectedGRN?.tds_info?.tax_value}%`}</span>
                                    </Fragment>
                                  )}
                                  className="view-document__totals-field-name"
                                />
                                <H3Text text={selectedGRN?.tcs_id ? `${MONEY((selectedGRN?.tcs_info?.tcs_amount || 0), selectedGRN?.org_currency_info?.currency_code)}` : `(-)${MONEY((selectedGRN?.tds_info?.tds_amount || 0), selectedGRN?.org_currency_info?.currency_code)}`} className={selectedGRN?.tcs_id ? 'view-document__totals-field-value' : '  view-document__totals-field-value danger-text'} />
                              </div>
                            )}
                            {this.renderFreight('bottom')}
                            {!isVendorOverseas && selectedGRN?.tax_info?.map((tax, i) => (
                              <div key={i} className="view-document__totals-field">
                                <H3Text text={tax?.tax_type_name} className="view-document__totals-field-nam" />
                                <H3Text text={MONEY((tax?.tax_amount), selectedGRN?.org_currency_info?.currency_code)} className="view-document__totals-field-value danger-text" />
                              </div>
                            ))}
                            {renderCharges(splitChargesData(selectedGRN?.other_charges).chargeWithoutTaxName)}
                            {Math.abs(selectedGRN?.grn_round_off) !== 0 && (
                              <div className="view-document__totals-field">
                                <H3Text text="Round Off" className="view-document__totals-field-name" />
                                {selectedGRN?.grn_round_off < 0
                                  ? (
                                    <H3Text
                                      text={`(-) ${MONEY((Math.abs(selectedGRN?.grn_round_off) || '0'), selectedGRN?.org_currency_info?.currency_code)}`}
                                      className="view-document__totals-field-value danger-text"
                                    />
                                  )
                                  : (
                                    <H3Text
                                      text={MONEY((selectedGRN?.grn_round_off || '0'), selectedGRN?.org_currency_info?.currency_code)}
                                      className="view-document__totals-field-value"
                                    />
                                  )}
                              </div>
                            )}
                            <div className="view-document__totals-field view-document__totals-field-total">
                              <H3Text text="Grand Total" className="view-document__totals-field-name" />
                              <H3Text text={MONEY((selectedGRN?.grn_grand_total), selectedGRN?.org_currency_info?.currency_code)} className="view-document__totals-field-value" />
                            </div>
                            {selectedGRN?.total_payment_made ? (
                              <div className="view-document__totals-field">
                                <H3Text text="Payment Made" className="view-document__totals-field-name" />
                                <H3Text text={`(-) ${MONEY((selectedGRN?.total_payment_made), selectedGRN?.org_currency_info?.currency_code)}`} className="view-document__totals-field-value danger-text" />
                              </div>
                            ) : <Fragment />}
                            {Helpers.getValueTotalInObject(selectedGRN?.debit_note_info, 'db_total') > 0 && (
                              <div className="view-document__totals-field">
                                <H3Text text="Credit Applied" className="view-document__totals-field-name" />
                                <H3Text
                                  text={`(-) ${MONEY(
                                    Helpers.getValueTotalInObject(selectedGRN?.debit_note_info, 'document_db_total')
                                    || Helpers.getValueTotalInObject(selectedGRN?.debit_note_info, 'db_total')
                                    || 0,
                                    selectedGRN?.org_currency_info?.currency_code,
                                  )}`}
                                  className="view-document__totals-field-value danger-text"
                                />
                              </div>
                            )}
                            <div className="view-document__totals-field view-document__totals-field-total">
                              <H3Text text="Total Due" className="view-document__totals-field-name" />
                              <H3Text text={MONEY((selectedGRN?.grn_grand_total - (Helpers.getValueTotalInObject(selectedGRN?.debit_note_info, 'document_db_total') || Helpers.getValueTotalInObject(selectedGRN?.debit_note_info, 'db_total')) - selectedGRN?.total_payment_made), selectedGRN?.org_currency_info?.currency_code)} className="view-document__totals-field-value" />
                            </div>
                          </div>
                        )}
                      </div> */}
                    </Fragment>
                  )}
                  {currentTab === '/grn-quality-checks' && (
                    <Fragment>
                      <QualityCheckGRN
                        callback={() => {
                          if (callback) callback();
                          getGRNById(Helpers.getTenantEntityPermission(user?.user_tenants, Helpers.permissionEntities.GOOD_RECEIVING, Helpers.permissionTypes.READ).join(','), selectedGrnId || match?.params?.grnId);
                        }}
                        selectedGRNLines={getQcData({ data: selectedGRN?.grn_lines, selectedGRN })}
                      />
                    </Fragment>
                  )}
                </div>
              </div>
              <ViewDocRightSection
                entityType="GOOD_RECEIVING_NOTE"
                entityId={selectedGRN?.grn_id}
                MONEY={MONEY}
                user={user}
                isQuickView={isQuickView}
                tagSelectorProps={{
                  isEnabled: true,
                  entityType: 'GRN',
                  selectedTags,
                  isMultipleTagsAllowed: true,
                  tagSearchAllowed: true,
                  onChange: (value) => this.setState({ selectedTags: value }),
                  placeholder: 'Select Tags',
                }}
                workflowTimelineProps={{
                  isEnabled: selectedGRN?.status !== 'DRAFT',
                  workflowSteps: selectedGRN?.workflow_steps || {},
                  workflowStepId: selectedGRN?.workflow_step_id,
                  loading: updateGRNWorkflowStepLoading,
                  updateStep: updateGRNWorkflowStep,
                  callback: () => {
                    getGRNById(Helpers.getTenantEntityPermission(user?.user_tenants, Helpers.permissionEntities.GOOD_RECEIVING, Helpers.permissionTypes.READ).join(','), selectedGrnId || match?.params?.grnId);
                  },
                }}
                activityLogProps={{
                  isEnabled: selectedGRN,
                  entityName: 'Good Receiving Note',
                  entityType: 'good_receiving_note',
                }}
                linkedDocumentsInfo={getLinkedDocumentInfo({
                  selectedGRN,
                  user,
                  priceMasking,
                  showViewPayout,
                  setState: (values) => this.setState(values),
                })}
              />
              {/* <div className={`${fixedMenuBar ? 'ant-col-md-8' : 'ant-col-md-7'} ant-col-xs-24`}>
                <div className={`view-right__wrapper ${!isQuickView ? 'is-page-view' : ''}`}>
                  {selectedGRN?.debit_notes?.length && Helpers.getPermission(Helpers.permissionEntities.DEBIT_NOTE, Helpers.permissionTypes.READ, user)
                    ? (
                      <div className="linked-doc__wrapper">
                        <H3Text text="ASSOCIATED DEBIT NOTES" className="linked-doc__title" />
                        {selectedGRN?.debit_notes?.map((item) => (
                          <div
                            key={item?.debit_note_number}
                            className="linked-doc__item-header"
                            onClick={() => {
                              window.open(`/purchase/debit-note/view/${item?.dn_id}`);
                            }}
                          >
                            <CaretRightOutlined />
                            <div className="linked-doc__item-header-id-status__wrapper">
                              <H3Text
                                text={`DN # ${item?.debit_note_number}`}
                                className="linked-doc__item-header-id"
                              />
                              <div
                                className="linked-doc__item-header-status"
                                style={{ backgroundColor: Helpers.getStatusColor(item?.status)?.color }}
                              >
                                {Helpers.getStatusColor(item?.status)?.text}
                              </div>
                            </div>
                            <H3Text
                              text={MONEY((item?.document_dn_grand_total || item?.dn_grand_total), selectedGRN?.org_currency_info?.currency_code)}
                              className={`linked-doc__item-header-total ${['VOID', 'REJECTED'].includes(item?.status) ? 'linked-doc__item-header-total-disabled' : ''}`}
                              hideText={isDataMaskingPolicyEnable && isHideCostPrice}
                              popOverMessage={"You don't have access to view  amount"}
                            />
                          </div>
                        ))}

                      </div>
                    )
                    : null}
                  {!(isDataMaskingPolicyEnable && (isHideCostPrice || isHideSellingPrice)) && selectedGRN?.grn_payments?.length
                    ? (
                      <div className="linked-doc__wrapper">
                        <H3Text text="LINKED PAYMENTS" className="linked-doc__title" />
                        {selectedGRN?.grn_payments?.map((item) => (
                          <div
                            key={item?.payment_id}
                            className="linked-doc__item-header"
                            onClick={() => this.setState({ selectedPayment: item, showViewPayout: true })}

                          >
                            <CaretRightOutlined />
                            <div className="linked-doc__item-header-id-status__wrapper">
                              <H3Text text={`${item?.applied_payment_type?.split('_')?.map((word) => word?.[0].toUpperCase())?.join('')} # ${item?.debit_note_number || item?.payment_id}`} className="linked-doc__item-header-id" />
                            </div>
                            <H3Text
                              text={MONEY((item?.amount))}
                              className={`linked-doc__item-header-total ${['VOID', 'REJECTED'].includes(item?.status) ? 'linked-doc__item-header-total-disabled' : ''}`}
                            />
                          </div>
                        ))}
                      </div>
                    )
                    : null}
                  {selectedGRN?.ap_invoices?.length > 0 && (
                    <div className="linked-doc__wrapper">
                      <H3Text text="Linked AP Invoices" className="linked-doc__title" />
                      {selectedGRN?.ap_invoices?.map((item) => (
                        <div
                          key={item?.ap_invoice_id}
                          className="linked-doc__item-header"
                          style={{ color: '#2d7df7' }}
                          onClick={() => {
                            window.open(`/purchase/account-payable-invoice/view/${item?.ap_invoice_id}`);
                          }}
                        >
                          <CaretRightOutlined />
                          <div className="linked-doc__item-header-id-status__wrapper">
                            <H3Text text={`# ${item?.ap_invoice_number}`} className="linked-doc__item-header-id" />
                          </div>
                          <H3Text
                            text={MONEY((item?.grand_total))}
                            className={`linked-doc__item-header-total ${['VOID', 'REJECTED'].includes(item?.status) ? 'linked-doc__item-header-total-disabled' : ''}`}
                          />
                        </div>
                      ))}
                    </div>
                  )}
                  Tags GRN
                  <TagSelector
                    entityType="GRN"
                    entityId={selectedGRN?.grn_id}
                    selectedTags={selectedTags}
                    isMultiple
                    showSearch
                    onChange={(value) => {
                      this.setState({ selectedTags: value });
                    }}
                    placeholder="Select Tags"
                    maxTagCount='responsive'
                  />
                  {selectedGRN?.status !== 'DRAFT' ? (
                    <WorkFlowTimeLine
                      workflowSteps={selectedGRN?.workflow_steps || {}}
                      workflowStepId={selectedGRN?.workflow_step_id}
                      updateWorkflowStep={updateGRNWorkflowStep}
                      loading={updateGRNWorkflowStepLoading}
                      entityId={selectedGRN?.grn_id}
                      entityType="GOOD_RECEIVING_NOTE"
                      HideCase={HideAdhocApprovals()}
                      callback={() => {
                        getGRNById(Helpers.getTenantEntityPermission(user?.user_tenants, Helpers.permissionEntities.GOOD_RECEIVING, Helpers.permissionTypes.READ).join(','), selectedGrnId || match?.params?.grnId);
                      }}
                    />
                  ) : ''}
                  {selectedGRN && (
                    <ActivityLog
                      entityId={selectedGRN?.grn_id}
                      entityType="good_receiving_note"
                      entityName="Good Receiving Note"
                    />
                  )}
                </div>
              </div> */}
            </div>
          </div>
        ) : (
          // Loader
          <ViewLoadingSkull isQuickView={isQuickView} fixedMenuBar={fixedMenuBar} />
        )}
        <Drawer
          onClose={() => this.setState({ openDNModalView: false })}
          open={openDNModalView}
          width="820px"
          destroyOnClose
        >
          <div className="custom-drawer__header-wrapper">
            <div className="custom-drawer__header" style={{ width: '775px' }}>
              <H3Text text="View Debit Note" className="custom-drawer__title" />
              <H3Image src={closeIcon} className="custom-drawer__close-icon" onClick={() => this.setState({ openDNModalView: false })} />
            </div>
          </div>
          <div style={{ marginTop: '-35px' }}>
            <ViewDebitNote
              selectedDNId={selectedDebitNote?.dn_id}
              callback={() => {
                this.setState({ openDNModalView: false });
                getGRNById(Helpers.getTenantEntityPermission(user?.user_tenants, Helpers.permissionEntities.GOOD_RECEIVING, Helpers.permissionTypes.READ).join(','), selectedGrnId || match?.params?.grnId);
              }}
              isQuickView
            />
          </div>
        </Drawer>
        <Drawer
          open={showViewPayout}
          onClose={() => this.setState({ showViewPayout: false })}
          width="1000px"
          destroyOnClose
        >
          <div className="custom-drawer__header-wrapper">
            <div className="custom-drawer__header" style={{ width: '950px' }}>
              <H3Text text={`View Payment #${selectedPayment?.debit_note_number || selectedPayment?.payment_id}`} className="custom-drawer__title" />
              <H3Image src={closeIcon} className="custom-drawer__close-icon" onClick={() => this.setState({ showViewPayout: false })} />
            </div>
          </div>
          <ViewPayment
            selectedPayment={selectedPayment}
            selectedPaymentId={selectedPayment?.payment_id}
            paymentType={selectedPayment?.applied_payment_type}
            callback={() => {
              this.setState({ showViewPayout: false });
              getGRNById(Helpers.getTenantEntityPermission(user?.user_tenants, Helpers.permissionEntities.GOOD_RECEIVING, Helpers.permissionTypes.READ).join(','), selectedGrnId || match?.params?.grnId);
            }}
          />
        </Drawer>
        <Drawer
          placement="right"
          onClose={() => this.setState({ showPrintBarcodeDrawer: false })}
          open={showPrintBarcodeDrawer}
          width="360px"
          destroyOnClose
        >
          <div className="custom-drawer__header-wrapper">
            <div className="custom-drawer__header" style={{ width: '310px' }}>
              <H3Text text="Print Barcodes" className="custom-drawer__title" />
              <H3Image src={closeIcon} className="custom-drawer__close-icon" onClick={() => this.setState({ showPrintBarcodeDrawer: false })} />
            </div>
          </div>
          <PrintBarcodes selectedRows={selectedRows} barcodeType="GRN" />
        </Drawer>
        <VendorRating
          selectedEntity={selectedGRN}
          selectedSeller={selectedGRN?.tenant_seller_info}
          openModal={showModal}
          isUpdateRating={isUpdateRating}
          closeModal={() => this.setState({ showModal: false })}
          updateCallback={() => {
            this.setState({ showModal: false });
            getGRNById(Helpers.getTenantEntityPermission(user?.user_tenants, Helpers.permissionEntities.GOOD_RECEIVING, Helpers.permissionTypes.READ).join(','), selectedGrnId || match?.params?.grnId);
          }}
        />
        <DownloadLabels
          documentType="good_receiving_note"
          documentId={selectedGRN?.grn_id}
          isModalOpen={isGenerateLabelModalOpen}
          toggleModal={() => this.setState({ isGenerateLabelModalOpen: !isGenerateLabelModalOpen })}
          setIsLabelAvailable={(val) => this.setState({ isLabelAvailable: val })}
          selectedGRN={selectedGRN}
        />
        <PRZModal
          isOpen={showPRZModal}
          onClose={() => {
            this.setState({ showPRZModal: false, selectedDocumentId: null });
          }}
          entityType="product"
          documentId={selectedDocumentId}
        />
      </Fragment>

    );
  }
}

const mapStateToProps = ({
  UserReducers, GRNReducers, PaymentOutgoingReducers, AttachmentReducers, ZohoIntegrationReducers, TallyIntegrationReducers, BusyIntegrationReducers,
}) => ({
  user: UserReducers.user,
  createGRNLoading: GRNReducers.createGRNLoading,
  MONEY: UserReducers.MONEY,
  updateGRNStatusLoading: GRNReducers.updateGRNStatusLoading,
  createAndLinkPaymentLoading: PaymentOutgoingReducers.createAndLinkPaymentLoading,
  getGRNByIdLoading: GRNReducers.getGRNByIdLoading,
  selectedGRN: GRNReducers.selectedGRN,
  deleteGrnLoading: GRNReducers.deleteGrnLoading,
  updateGRNWorkflowStepLoading: GRNReducers?.updateGRNWorkflowStepLoading,
  unLinkPaymentLoading: PaymentOutgoingReducers.unLinkPaymentLoading,
  selectedAttachment: AttachmentReducers.selectedAttachment,
  getAttachmentByIdLoading: AttachmentReducers.getAttachmentByIdLoading,
  updateAttachmentLoading: AttachmentReducers.updateAttachmentLoading,
  syncGRNLoading: ZohoIntegrationReducers.syncGRNLoading,
  syncTallyGRNLoading: TallyIntegrationReducers.syncTallyGRNLoading,
  pullTallyVendorPaymentLoading: TallyIntegrationReducers.pullTallyVendorPaymentLoading,
  pushGrnToBusyLoading: BusyIntegrationReducers.pushGrnToBusyLoading,
  quickUpdateGRNLoading: GRNReducers.quickUpdateGRNLoading,
  priceMasking: UserReducers.priceMasking,
});

const mapDispatchToProps = (dispatch) => ({
  getGRNById: (tenantId, grnId) => dispatch(GRNActions.getGRNById(tenantId, grnId)),
  addGRN: (payload, action) => dispatch(GRNActions.createGRN(payload, action)),
  updateGRNStatus: (payload, action) => dispatch(GRNActions.updateGRNStatus(payload, action)),
  deleteGrn: (tenantId, grnId, callback) => dispatch(GRNActions.deleteGrn(tenantId, grnId, callback)),
  createAndLinkPayment: (payload, callback) => dispatch(PaymentOutgoingActions.createAndLinkPayment(payload, callback)),
  unLinkPayment: (grnPaymentId, callback) => dispatch(PaymentOutgoingActions.unLinkPayment(grnPaymentId, callback)),
  getPaymentsOutgoingSuccess: (payments) => dispatch(PaymentOutgoingActions.getPaymentsOutgoingSuccess(payments)),
  getAttachmentById: (entityId, entityName, callback) => dispatch(
    AttachmentActions.getAttachmentById(entityId, entityName, callback),
  ),
  updateAttachment: (payload, callback) => dispatch(
    AttachmentActions.updateAttachment(payload, callback),
  ),
  getGRNByIdSuccess: (grnData) => dispatch(GRNActions.getGRNByIdSuccess(grnData)),
  syncGRN: (payload, callback) => dispatch(ZohoIntegrationActions.syncGRN(payload, callback)),
  syncTallyGRN: (payload, callback) => dispatch(TallyIntegrationActions.syncTallyGRN(payload, callback)),
  pullTallyVendorPayment: (payload, callback) => dispatch(TallyIntegrationActions.pullTallyVendorPayment(payload, callback)),
  getActivityLogSuccess: (activityLog) => dispatch(ActivityLogActions.getActivityLogSuccess(activityLog)),
  updateGRNWorkflowStep: (payload, callback) => dispatch(GRNActions.updateGRNWorkflowStep(payload, callback)),
  pushGrnToBusy: (payload, callback) => dispatch(BusyIntegrationActions.pushGrnToBusy(payload, callback)),
  quickUpdateGRN: (payload, callback, rollback) => dispatch(GRNActions.quickUpdateGRN(payload, callback, rollback)),
  getDownloadQualityChecks: (tenantId, qualityCheckId, entityName, entityId) => dispatch(QualityCheckActions.getDownloadQualityChecks(tenantId, qualityCheckId, entityName, entityId)),
  downloadDocument: (payload, document) => dispatch(AnalyticsActions.downloadDocument(payload, document)),
  getActivityLog: (entityId, entityName, page, limit, callback) => dispatch(ActivityLogActions.getActivityLog(entityId, entityName, page, limit, callback)),
});

export default connect(mapStateToProps, mapDispatchToProps)(withRouter(ViewGRN));
